import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100');
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let whereConditions = [];
    let queryParams = [];

    // Build WHERE conditions
    if (role && role !== 'all') {
      whereConditions.push('role = ?');
      queryParams.push(role);
    }

    if (status && status !== 'all') {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    if (search) {
      whereConditions.push('(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users
      ${whereClause}
    `;

    const [countResult] = await db.execute(countQuery, queryParams);
    const totalUsers = (countResult as any[])[0]?.total || 0;

    // Get users with pagination
    const offset = (page - 1) * limit;
    const usersQuery = `
      SELECT 
        user_id,
        email,
        first_name,
        last_name,
        phone,
        role,
        status,
        is_verified,
        restriction_status,
        restriction_count,
        last_restriction_check,
        created_at,
        updated_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(limit, offset);
    const [users] = await db.execute(usersQuery, queryParams);

    // Format the results
    const formattedUsers = (users as any[]).map(user => ({
      user_id: user.user_id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone: user.phone,
      role: user.role,
      status: user.status,
      is_verified: Boolean(user.is_verified),
      restriction_status: user.restriction_status,
      restriction_count: user.restriction_count || 0,
      last_restriction_check: user.last_restriction_check,
      created_at: user.created_at,
      updated_at: user.updated_at
    }));

    return NextResponse.json({
      users: formattedUsers,
      pagination: {
        page,
        limit,
        total: totalUsers,
        totalPages: Math.ceil(totalUsers / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
