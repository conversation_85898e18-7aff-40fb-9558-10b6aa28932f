-- Update users table to add restriction-related fields for performance optimization
-- This migration adds fields to track restriction status and last check time

-- Add restriction_status field for quick status lookup
ALTER TABLE users ADD COLUMN restriction_status ENUM(
  'none',
  'account_suspension',
  'booking_restrictions', 
  'service_provider_restrictions',
  'communication_restrictions',
  'payment_restrictions',
  'profile_restrictions',
  'review_restrictions',
  'multiple_restrictions'
) DEFAULT 'none' COMMENT 'Current restriction status for quick lookup';

-- Add last_restriction_check field for caching optimization
ALTER TABLE users ADD COLUMN last_restriction_check TIMESTAMP NULL DEFAULT NULL COMMENT 'Last time restrictions were checked for this user';

-- Add restriction_count field for quick violation tracking
ALTER TABLE users ADD COLUMN restriction_count INT DEFAULT 0 COMMENT 'Total number of active restrictions';

-- Add index for restriction status queries
CREATE INDEX idx_users_restriction_status ON users(restriction_status);
CREATE INDEX idx_users_restriction_check ON users(last_restriction_check);
CREATE INDEX idx_users_restriction_count ON users(restriction_count);

-- Create a composite index for restriction queries
CREATE INDEX idx_users_restrictions_composite ON users(restriction_status, restriction_count, last_restriction_check);

-- Update existing users to have 'none' restriction status
UPDATE users SET restriction_status = 'none' WHERE restriction_status IS NULL;

-- Create a trigger to automatically update restriction_count when restrictions change
DELIMITER //
CREATE TRIGGER update_user_restriction_count 
AFTER INSERT ON user_restrictions
FOR EACH ROW
BEGIN
  DECLARE restriction_count INT DEFAULT 0;
  DECLARE primary_restriction VARCHAR(50) DEFAULT 'none';
  
  -- Count active restrictions for this user
  SELECT COUNT(*) INTO restriction_count
  FROM user_restrictions 
  WHERE user_id = NEW.user_id AND status = 'active';
  
  -- Determine primary restriction type
  IF restriction_count > 1 THEN
    SET primary_restriction = 'multiple_restrictions';
  ELSEIF restriction_count = 1 THEN
    SELECT restriction_type INTO primary_restriction
    FROM user_restrictions 
    WHERE user_id = NEW.user_id AND status = 'active'
    ORDER BY severity DESC, created_at DESC
    LIMIT 1;
  ELSE
    SET primary_restriction = 'none';
  END IF;
  
  -- Update user table
  UPDATE users 
  SET 
    restriction_count = restriction_count,
    restriction_status = primary_restriction,
    last_restriction_check = NOW()
  WHERE user_id = NEW.user_id;
END//

CREATE TRIGGER update_user_restriction_count_update
AFTER UPDATE ON user_restrictions
FOR EACH ROW
BEGIN
  DECLARE restriction_count INT DEFAULT 0;
  DECLARE primary_restriction VARCHAR(50) DEFAULT 'none';
  
  -- Count active restrictions for this user
  SELECT COUNT(*) INTO restriction_count
  FROM user_restrictions 
  WHERE user_id = NEW.user_id AND status = 'active';
  
  -- Determine primary restriction type
  IF restriction_count > 1 THEN
    SET primary_restriction = 'multiple_restrictions';
  ELSEIF restriction_count = 1 THEN
    SELECT restriction_type INTO primary_restriction
    FROM user_restrictions 
    WHERE user_id = NEW.user_id AND status = 'active'
    ORDER BY severity DESC, created_at DESC
    LIMIT 1;
  ELSE
    SET primary_restriction = 'none';
  END IF;
  
  -- Update user table
  UPDATE users 
  SET 
    restriction_count = restriction_count,
    restriction_status = primary_restriction,
    last_restriction_check = NOW()
  WHERE user_id = NEW.user_id;
END//

CREATE TRIGGER update_user_restriction_count_delete
AFTER DELETE ON user_restrictions
FOR EACH ROW
BEGIN
  DECLARE restriction_count INT DEFAULT 0;
  DECLARE primary_restriction VARCHAR(50) DEFAULT 'none';
  
  -- Count active restrictions for this user
  SELECT COUNT(*) INTO restriction_count
  FROM user_restrictions 
  WHERE user_id = OLD.user_id AND status = 'active';
  
  -- Determine primary restriction type
  IF restriction_count > 1 THEN
    SET primary_restriction = 'multiple_restrictions';
  ELSEIF restriction_count = 1 THEN
    SELECT restriction_type INTO primary_restriction
    FROM user_restrictions 
    WHERE user_id = OLD.user_id AND status = 'active'
    ORDER BY severity DESC, created_at DESC
    LIMIT 1;
  ELSE
    SET primary_restriction = 'none';
  END IF;
  
  -- Update user table
  UPDATE users 
  SET 
    restriction_count = restriction_count,
    restriction_status = primary_restriction,
    last_restriction_check = NOW()
  WHERE user_id = OLD.user_id;
END//
DELIMITER ;

-- Create a stored procedure to refresh restriction status for all users
DELIMITER //
CREATE PROCEDURE RefreshUserRestrictionStatus()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE user_id_var INT;
  DECLARE restriction_count INT DEFAULT 0;
  DECLARE primary_restriction VARCHAR(50) DEFAULT 'none';

  -- Cursor to iterate through all users
  DECLARE user_cursor CURSOR FOR
    SELECT user_id FROM users;

  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

  OPEN user_cursor;

  user_loop: LOOP
    FETCH user_cursor INTO user_id_var;
    IF done THEN
      LEAVE user_loop;
    END IF;

    -- Count active restrictions for this user
    SELECT COUNT(*) INTO restriction_count
    FROM user_restrictions
    WHERE user_id = user_id_var AND status = 'active';

    -- Determine primary restriction type
    IF restriction_count > 1 THEN
      SET primary_restriction = 'multiple_restrictions';
    ELSEIF restriction_count = 1 THEN
      SELECT restriction_type INTO primary_restriction
      FROM user_restrictions
      WHERE user_id = user_id_var AND status = 'active'
      ORDER BY severity DESC, created_at DESC
      LIMIT 1;
    ELSE
      SET primary_restriction = 'none';
    END IF;

    -- Update user table
    UPDATE users
    SET
      restriction_count = restriction_count,
      restriction_status = primary_restriction,
      last_restriction_check = NOW()
    WHERE user_id = user_id_var;

  END LOOP;

  CLOSE user_cursor;
END//
DELIMITER ;

-- Run the procedure to initialize restriction status for existing users
CALL RefreshUserRestrictionStatus();

-- Add comments to the new columns
ALTER TABLE users MODIFY COLUMN restriction_status ENUM(
  'none',
  'account_suspension',
  'booking_restrictions',
  'service_provider_restrictions',
  'communication_restrictions',
  'payment_restrictions',
  'profile_restrictions',
  'review_restrictions',
  'multiple_restrictions'
) DEFAULT 'none' COMMENT 'Current primary restriction status for quick lookup and caching';

ALTER TABLE users MODIFY COLUMN last_restriction_check TIMESTAMP NULL DEFAULT NULL COMMENT 'Last time restrictions were checked for this user - used for caching optimization';

ALTER TABLE users MODIFY COLUMN restriction_count INT DEFAULT 0 COMMENT 'Total number of active restrictions for this user';

-- Create appeal_messages table for admin-user communication
CREATE TABLE IF NOT EXISTS appeal_messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  appeal_id INT NOT NULL,
  sender_id INT NOT NULL,
  message TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT FALSE COMMENT 'Internal messages only visible to admins',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (appeal_id) REFERENCES restriction_appeals(id) ON DELETE CASCADE,
  FOREIGN KEY (sender_id) REFERENCES users(user_id) ON DELETE CASCADE,

  INDEX idx_appeal_messages_appeal_id (appeal_id),
  INDEX idx_appeal_messages_sender_id (sender_id),
  INDEX idx_appeal_messages_created_at (created_at),
  INDEX idx_appeal_messages_internal (is_internal)
) COMMENT 'Messages between admins and users for appeal communication';
