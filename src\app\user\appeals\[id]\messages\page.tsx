'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { 
  ArrowLeftIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { getAuthToken } from '@/utils/auth';
import AppealMessaging from '@/components/appeals/AppealMessaging';

interface Appeal {
  id: number;
  restriction_id: number;
  restriction_type: string;
  reason_category: string;
  appeal_reason: string;
  status: string;
  priority: string;
  submitted_at: string;
  reviewed_at: string | null;
  admin_response: string | null;
  response_deadline: string | null;
}

export default function AppealMessagesPage() {
  const params = useParams();
  const router = useRouter();
  const appealId = parseInt(params.id as string);
  
  const [appeal, setAppeal] = useState<Appeal | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<number | null>(null);

  useEffect(() => {
    // Get current user ID from auth token
    const authToken = getAuthToken();
    if (authToken) {
      const [userId] = authToken.split('_');
      setCurrentUserId(parseInt(userId));
    }

    fetchAppeal();
  }, [appealId]);

  const fetchAppeal = async () => {
    try {
      const response = await fetch(`/api/user/appeals`);
      if (!response.ok) throw new Error('Failed to fetch appeals');
      
      const data = await response.json();
      const foundAppeal = data.appeals.find((a: Appeal) => a.id === appealId);
      
      if (!foundAppeal) {
        toast.error('Appeal not found');
        router.push('/user/restriction-status');
        return;
      }

      setAppeal(foundAppeal);
    } catch (error) {
      console.error('Error fetching appeal:', error);
      toast.error('Failed to load appeal');
      router.push('/user/restriction-status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'under_review':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'denied':
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case 'escalated':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'text-blue-600 bg-blue-100';
      case 'under_review': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'denied': return 'text-red-600 bg-red-100';
      case 'escalated': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatRestrictionType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="bg-white rounded-xl shadow-md p-6 mb-6">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
            <div className="h-96 bg-gray-200 rounded-xl"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!appeal || !currentUserId) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Appeal Not Found</h2>
            <p className="text-gray-600 mb-4">
              The appeal you're looking for doesn't exist or you don't have access to it.
            </p>
            <button
              onClick={() => router.push('/user/restriction-status')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Back to Restriction Status
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => router.push('/user/restriction-status')}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back to Restriction Status
          </button>
          
          <h1 className="text-2xl font-bold text-gray-900">Appeal Communication</h1>
          <p className="text-gray-600">
            Communicate with our support team about your appeal
          </p>
        </div>

        {/* Appeal Summary */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Appeal for {formatRestrictionType(appeal.restriction_type)}
              </h2>
              <div className="flex items-center gap-2">
                {getStatusIcon(appeal.status)}
                <span className={`px-2 py-1 text-sm font-medium rounded-full ${getStatusColor(appeal.status)}`}>
                  {appeal.status.replace('_', ' ')}
                </span>
              </div>
            </div>
            <div className="text-right text-sm text-gray-600">
              <p>Appeal ID: {appeal.id}</p>
              <p>Submitted: {formatDate(appeal.submitted_at)}</p>
              {appeal.response_deadline && (
                <p>Response Deadline: {formatDate(appeal.response_deadline)}</p>
              )}
            </div>
          </div>

          <div className="border-t border-gray-200 pt-4">
            <h3 className="font-medium text-gray-900 mb-2">Your Appeal Reason</h3>
            <p className="text-gray-600 bg-gray-50 rounded-lg p-3 text-sm">
              {appeal.appeal_reason}
            </p>
          </div>

          {appeal.admin_response && (
            <div className="border-t border-gray-200 pt-4 mt-4">
              <h3 className="font-medium text-gray-900 mb-2">Admin Response</h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-blue-800 text-sm">{appeal.admin_response}</p>
                {appeal.reviewed_at && (
                  <p className="text-blue-600 text-xs mt-2">
                    Reviewed on {formatDate(appeal.reviewed_at)}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Messaging Component */}
        <AppealMessaging
          appealId={appealId}
          currentUserRole="user"
          currentUserId={currentUserId}
        />

        {/* Help Text */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">Communication Guidelines</h3>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>• Be respectful and professional in all communications</li>
            <li>• Provide any additional information that might help your case</li>
            <li>• Response times may vary, but we aim to respond within 24-48 hours</li>
            <li>• You can continue messaging until your appeal is resolved</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
