import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logAdminAction } from '@/services/auditService';
// import { sendUserNotification } from '@/utils/notificationService';

// Validation schema for appeal review
const _appealReviewSchema = z.object({
  decision: z.enum(['approved', 'denied', 'escalated']),
  adminResponse: z.string().min(10).max(2000),
  internalNotes: z.string().optional(),
  escalationReason: z.string().optional(),
  escalateToAdminId: z.number().optional()
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const assignedToMe = searchParams.get('assignedToMe') === 'true';
    const search = searchParams.get('search');
    
    const offset = (page - 1) * limit;
    const adminId = parseInt(session.user.id);

    // Build query
    let baseQuery = `
      SELECT 
        a.*,
        u.first_name,
        u.last_name,
        u.email,
        r.restriction_type,
        r.reason_category,
        r.severity,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      JOIN user_restrictions r ON a.restriction_id = r.id
      LEFT JOIN users admin ON a.assigned_admin_id = admin.user_id
    `;

    let whereConditions = [];
    let queryParams = [];

    // Filter by status
    if (status && ['submitted', 'under_review', 'approved', 'denied', 'escalated'].includes(status)) {
      whereConditions.push('a.status = ?');
      queryParams.push(status);
    }

    // Filter by priority
    if (priority && ['low', 'medium', 'high', 'urgent'].includes(priority)) {
      whereConditions.push('a.priority = ?');
      queryParams.push(priority);
    }

    // Filter by assigned admin
    if (assignedToMe) {
      whereConditions.push('a.assigned_admin_id = ?');
      queryParams.push(adminId);
    }

    // Search functionality
    if (search) {
      whereConditions.push(`(
        u.first_name LIKE ? OR 
        u.last_name LIKE ? OR 
        u.email LIKE ? OR
        a.appeal_reason LIKE ?
      )`);
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Combine conditions
    if (whereConditions.length > 0) {
      baseQuery += ' WHERE ' + whereConditions.join(' AND ');
    }

    // Add ordering and pagination
    baseQuery += ' ORDER BY a.submitted_at DESC LIMIT ? OFFSET ?';
    queryParams.push(limit, offset);

    // Execute query
    const [appeals] = await db.execute(baseQuery, queryParams);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      JOIN user_restrictions r ON a.restriction_id = r.id
    `;

    let countParams = [];
    if (whereConditions.length > 0) {
      countQuery += ' WHERE ' + whereConditions.join(' AND ');
      countParams = queryParams.slice(0, -2);
    }

    const [countResult] = await db.execute(countQuery, countParams);
    const total = (countResult as any)[0].total;

    // Format response
    const formattedAppeals = (appeals as any[]).map(appeal => ({
      id: appeal.id,
      restrictionId: appeal.restriction_id,
      user: {
        id: appeal.user_id,
        name: `${appeal.first_name} ${appeal.last_name}`,
        email: appeal.email
      },
      restriction: {
        type: appeal.restriction_type,
        reasonCategory: appeal.reason_category,
        severity: appeal.severity
      },
      appealReason: appeal.appeal_reason,
      status: appeal.status,
      priority: appeal.priority,
      assignedAdmin: appeal.admin_first_name ? {
        id: appeal.assigned_admin_id,
        name: `${appeal.admin_first_name} ${appeal.admin_last_name}`
      } : null,
      adminResponse: appeal.admin_response,
      submittedAt: appeal.submitted_at,
      reviewedAt: appeal.reviewed_at,
      responseDeadline: appeal.response_deadline,
      createdAt: appeal.created_at,
      updatedAt: appeal.updated_at
    }));

    return NextResponse.json({
      appeals: formattedAppeals,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching appeals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch appeals' },
      { status: 500 }
    );
  }
}

// POST endpoint to assign appeals to admins
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const adminId = parseInt(session.user.id);
    const body = await request.json();
    const { appealId, action, assignToAdminId } = body;

    if (!appealId || !action) {
      return NextResponse.json(
        { error: 'Appeal ID and action are required' },
        { status: 400 }
      );
    }

    // Get appeal details
    const [appeals] = await db.execute(`
      SELECT
        a.*,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        u.email as user_email,
        r.restriction_type
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      JOIN user_restrictions r ON a.restriction_id = r.id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(appeals) || appeals.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = appeals[0] as any;

    switch (action) {
      case 'assign':
        if (!assignToAdminId) {
          return NextResponse.json(
            { error: 'Admin ID is required for assignment' },
            { status: 400 }
          );
        }

        // Update appeal assignment
        await db.execute(`
          UPDATE restriction_appeals
          SET
            assigned_admin_id = ?,
            status = 'under_review',
            updated_at = NOW()
          WHERE id = ?
        `, [assignToAdminId, appealId]);

        // Log the assignment
        await logAdminAction({
          adminId,
          actionType: 'appeal_assigned',
          targetType: 'restriction_appeal',
          targetId: appealId,
          affectedUserId: appeal.user_id,
          actionDetails: {
            assigned_to_admin_id: assignToAdminId,
            appeal_status: 'under_review'
          },
          reason: 'Appeal assigned for review',
          severity: 'low'
        });

        return NextResponse.json({
          success: true,
          message: 'Appeal assigned successfully'
        });

      case 'take':
        // Assign to current admin
        await db.execute(`
          UPDATE restriction_appeals
          SET
            assigned_admin_id = ?,
            status = 'under_review',
            updated_at = NOW()
          WHERE id = ?
        `, [adminId, appealId]);

        // Log the assignment
        await logAdminAction({
          adminId,
          actionType: 'appeal_assigned',
          targetType: 'restriction_appeal',
          targetId: appealId,
          affectedUserId: appeal.user_id,
          actionDetails: {
            assigned_to_admin_id: adminId,
            appeal_status: 'under_review',
            self_assigned: true
          },
          reason: 'Admin took appeal for review',
          severity: 'low'
        });

        return NextResponse.json({
          success: true,
          message: 'Appeal assigned to you successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing appeal:', error);
    return NextResponse.json(
      { error: 'Failed to manage appeal' },
      { status: 500 }
    );
  }
}
