'use client';

import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { 
  PaperAirplaneIcon,
  UserIcon,
  ShieldCheckIcon,
  EyeSlashIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

interface Message {
  id: number;
  appeal_id: number;
  sender: {
    id: number;
    name: string;
    role: string;
  };
  message: string;
  is_internal: boolean;
  created_at: string;
  updated_at: string;
}

interface Appeal {
  id: number;
  user: {
    id: number;
    name: string;
    email: string;
  };
  status: string;
}

interface AppealMessagingProps {
  appealId: number;
  currentUserRole: string;
  currentUserId: number;
}

export default function AppealMessaging({ 
  appealId, 
  currentUserRole, 
  currentUserId 
}: AppealMessagingProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [appeal, setAppeal] = useState<Appeal | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const isAdmin = currentUserRole === 'admin';

  useEffect(() => {
    fetchMessages();
  }, [appealId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/admin/appeals/${appealId}/messages`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      
      const data = await response.json();
      setMessages(data.messages || []);
      setAppeal(data.appeal);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim()) {
      toast.error('Please enter a message');
      return;
    }

    setSending(true);

    try {
      const response = await fetch(`/api/admin/appeals/${appealId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: newMessage.trim(),
          isInternal: isAdmin && isInternal
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send message');
      }

      setNewMessage('');
      setIsInternal(false);
      fetchMessages(); // Refresh messages
      toast.success('Message sent successfully');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canSendMessages = () => {
    if (!appeal) return false;
    if (isAdmin) return true; // Admins can always send messages
    return !['approved', 'denied'].includes(appeal.status); // Users can't message closed appeals
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-md flex flex-col h-96">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">
            Appeal Communication
          </h3>
          {appeal && (
            <span className="text-sm text-gray-500">
              with {isAdmin ? appeal.user.name : 'Support Team'}
            </span>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => {
            const isCurrentUser = message.sender.id === currentUserId;
            const isAdminMessage = message.sender.role === 'admin';

            return (
              <div
                key={message.id}
                className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  isCurrentUser 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  {/* Message Header */}
                  <div className="flex items-center gap-2 mb-1">
                    {isAdminMessage ? (
                      <ShieldCheckIcon className="h-4 w-4" />
                    ) : (
                      <UserIcon className="h-4 w-4" />
                    )}
                    <span className="text-xs font-medium">
                      {message.sender.name}
                    </span>
                    {message.is_internal && (
                      <div className="flex items-center gap-1">
                        <EyeSlashIcon className="h-3 w-3" />
                        <span className="text-xs">Internal</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Message Content */}
                  <p className="text-sm whitespace-pre-wrap">{message.message}</p>
                  
                  {/* Message Time */}
                  <p className={`text-xs mt-1 ${
                    isCurrentUser ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatDate(message.created_at)}
                  </p>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      {canSendMessages() ? (
        <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
          {/* Internal Message Toggle (Admin Only) */}
          {isAdmin && (
            <div className="mb-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isInternal}
                  onChange={(e) => setIsInternal(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700 flex items-center gap-1">
                  <EyeSlashIcon className="h-4 w-4" />
                  Internal message (only visible to admins)
                </span>
              </label>
            </div>
          )}

          <div className="flex gap-2">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              rows={2}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              disabled={sending}
            />
            <button
              type="submit"
              disabled={sending || !newMessage.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
              {sending ? 'Sending...' : 'Send'}
            </button>
          </div>
        </form>
      ) : (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <p className="text-sm text-gray-600 text-center">
            This appeal has been closed. No new messages can be sent.
          </p>
        </div>
      )}
    </div>
  );
}
