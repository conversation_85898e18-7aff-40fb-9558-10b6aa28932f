'use client';

import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { 
  DocumentTextIcon,
  PaperClipIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface Restriction {
  id: number;
  restriction_type: string;
  reason_category: string;
  custom_reason: string;
  severity: string;
  start_date: string;
  end_date: string | null;
  is_permanent: boolean;
  appeal_deadline: string | null;
}

interface AppealSubmissionFormProps {
  restriction: Restriction;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function AppealSubmissionForm({ 
  restriction, 
  onSuccess, 
  onCancel 
}: AppealSubmissionFormProps) {
  const [formData, setFormData] = useState({
    appealReason: '',
    supportingDocuments: [] as string[]
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];

    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File ${file.name} has an unsupported format.`);
        return false;
      }
      return true;
    });

    setUploadedFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.appealReason.trim().length < 10) {
      toast.error('Appeal reason must be at least 10 characters long');
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload files first if any
      const uploadedFilePaths = [];
      
      for (const file of uploadedFiles) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'appeal_document');

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData
        });

        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          uploadedFilePaths.push(uploadResult.filePath);
        } else {
          console.error('Failed to upload file:', file.name);
        }
      }

      // Submit the appeal
      const response = await fetch('/api/user/appeals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restrictionId: restriction.id,
          appealReason: formData.appealReason,
          supportingDocuments: uploadedFilePaths
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to submit appeal');
      }

      toast.success('Appeal submitted successfully! We will review it within 3-5 business days.');
      onSuccess();
    } catch (error) {
      console.error('Error submitting appeal:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit appeal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatRestrictionType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor': return 'text-yellow-600 bg-yellow-100';
      case 'moderate': return 'text-orange-600 bg-orange-100';
      case 'severe': return 'text-red-600 bg-red-100';
      case 'critical': return 'text-red-800 bg-red-200';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const isDeadlinePassed = restriction.appeal_deadline && 
    new Date(restriction.appeal_deadline) < new Date();

  if (isDeadlinePassed) {
    return (
      <div className="bg-white rounded-xl shadow-md p-6">
        <div className="flex items-center gap-3 mb-4">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          <h3 className="text-lg font-medium text-gray-900">Appeal Deadline Passed</h3>
        </div>
        <p className="text-gray-600 mb-4">
          The deadline to appeal this restriction has passed. If you believe this is an error, 
          please contact support directly.
        </p>
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-md p-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">Submit Appeal</h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Restriction Details */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h4 className="font-medium text-gray-900 mb-2">Restriction Details</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Type:</span>
            <span className="font-medium">{formatRestrictionType(restriction.restriction_type)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Severity:</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(restriction.severity)}`}>
              {restriction.severity}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Reason:</span>
            <span className="font-medium text-right max-w-xs">{restriction.custom_reason}</span>
          </div>
          {restriction.appeal_deadline && (
            <div className="flex justify-between">
              <span className="text-gray-600">Appeal Deadline:</span>
              <span className="font-medium">
                {new Date(restriction.appeal_deadline).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Appeal Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Appeal Reason *
          </label>
          <textarea
            value={formData.appealReason}
            onChange={(e) => setFormData({...formData, appealReason: e.target.value})}
            required
            rows={6}
            placeholder="Please explain why you believe this restriction should be lifted. Be specific and provide as much detail as possible..."
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Minimum 10 characters. Be clear and detailed in your explanation.
          </p>
        </div>

        {/* Supporting Documents */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Supporting Documents (Optional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
            <input
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.gif,.pdf,.txt"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer flex flex-col items-center"
            >
              <PaperClipIcon className="h-8 w-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-600">
                Click to upload files or drag and drop
              </span>
              <span className="text-xs text-gray-500 mt-1">
                Images, PDFs, or text files (max 10MB each)
              </span>
            </label>
          </div>

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && (
            <div className="mt-3 space-y-2">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-2">
                  <div className="flex items-center gap-2">
                    <DocumentTextIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(file.size / 1024 / 1024).toFixed(1)} MB)
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Appeal Process Information</p>
              <ul className="list-disc list-inside space-y-1 text-blue-700">
                <li>Appeals are typically reviewed within 3-5 business days</li>
                <li>You will receive an email notification when your appeal is reviewed</li>
                <li>Providing supporting evidence can help strengthen your appeal</li>
                <li>You can only submit one appeal per restriction</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || formData.appealReason.trim().length < 10}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Appeal'}
          </button>
        </div>
      </form>
    </div>
  );
}
