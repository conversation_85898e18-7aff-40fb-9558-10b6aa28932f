import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logAdminAction } from '@/services/auditService';
import { sendUserNotification } from '@/utils/notificationService';

// Validation schema for message
const messageSchema = z.object({
  message: z.string().min(1).max(2000),
  isInternal: z.boolean().optional().default(false)
});

// GET endpoint to fetch messages for an appeal
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const appealId = parseInt(params.id);
    const userId = parseInt(session.user.id);
    const isAdmin = session.user.role === 'admin';

    // Verify appeal exists and user has access
    const [appeals] = await db.execute(`
      SELECT 
        a.id,
        a.user_id,
        a.status,
        u.first_name,
        u.last_name,
        u.email
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(appeals) || appeals.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = appeals[0] as any;

    // Check access permissions
    if (!isAdmin && appeal.user_id !== userId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get messages (filter internal messages for non-admin users)
    let messagesQuery = `
      SELECT 
        m.*,
        sender.first_name as sender_first_name,
        sender.last_name as sender_last_name,
        sender.role as sender_role
      FROM appeal_messages m
      JOIN users sender ON m.sender_id = sender.user_id
      WHERE m.appeal_id = ?
    `;

    if (!isAdmin) {
      messagesQuery += ' AND m.is_internal = FALSE';
    }

    messagesQuery += ' ORDER BY m.created_at ASC';

    const [messages] = await db.execute(messagesQuery, [appealId]);

    // Format messages
    const formattedMessages = (messages as any[]).map(message => ({
      id: message.id,
      appeal_id: message.appeal_id,
      sender: {
        id: message.sender_id,
        name: `${message.sender_first_name} ${message.sender_last_name}`,
        role: message.sender_role
      },
      message: message.message,
      is_internal: Boolean(message.is_internal),
      created_at: message.created_at,
      updated_at: message.updated_at
    }));

    return NextResponse.json({
      appeal: {
        id: appeal.id,
        user: {
          id: appeal.user_id,
          name: `${appeal.first_name} ${appeal.last_name}`,
          email: appeal.email
        },
        status: appeal.status
      },
      messages: formattedMessages
    });

  } catch (error) {
    console.error('Error fetching appeal messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}

// POST endpoint to send a message
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const appealId = parseInt(params.id);
    const senderId = parseInt(session.user.id);
    const isAdmin = session.user.role === 'admin';
    const body = await request.json();

    // Validate input
    const validatedData = messageSchema.parse(body);

    // Verify appeal exists and user has access
    const [appeals] = await db.execute(`
      SELECT 
        a.id,
        a.user_id,
        a.status,
        u.first_name,
        u.last_name,
        u.email
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(appeals) || appeals.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = appeals[0] as any;

    // Check access permissions
    if (!isAdmin && appeal.user_id !== senderId) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if appeal allows messaging
    if (['approved', 'denied'].includes(appeal.status) && !isAdmin) {
      return NextResponse.json(
        { error: 'Cannot send messages to closed appeals' },
        { status: 400 }
      );
    }

    // Only admins can send internal messages
    const isInternal = isAdmin && validatedData.isInternal;

    // Insert message
    const [result] = await db.execute(`
      INSERT INTO appeal_messages (
        appeal_id, sender_id, message, is_internal, created_at
      ) VALUES (?, ?, ?, ?, NOW())
    `, [appealId, senderId, validatedData.message, isInternal]);

    const messageId = (result as any).insertId;

    // Update appeal's last activity
    await db.execute(`
      UPDATE restriction_appeals
      SET updated_at = NOW()
      WHERE id = ?
    `, [appealId]);

    // Log the message
    await logAdminAction({
      adminId: isAdmin ? senderId : null,
      actionType: 'appeal_message_sent',
      targetType: 'appeal_message',
      targetId: messageId,
      affectedUserId: appeal.user_id,
      actionDetails: {
        appeal_id: appealId,
        message_length: validatedData.message.length,
        is_internal: isInternal,
        sender_role: session.user.role
      },
      reason: `Message sent in appeal communication`,
      severity: 'low'
    });

    // Send notification to the other party (if not internal message)
    if (!isInternal) {
      if (isAdmin) {
        // Admin sent message to user
        await sendUserNotification({
          userId: appeal.user_id,
          type: 'appeal_message',
          title: 'New Message on Your Appeal',
          message: 'An administrator has sent you a message regarding your appeal.',
          data: {
            appeal_id: appealId,
            message_id: messageId
          }
        });
      } else {
        // User sent message to admin - notify assigned admin or all admins
        // This would typically be handled by a separate admin notification system
        console.log(`User ${senderId} sent message on appeal ${appealId}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Message sent successfully',
      messageId: messageId
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }

    console.error('Error sending appeal message:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}
