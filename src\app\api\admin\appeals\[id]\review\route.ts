import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logAdminAction } from '@/services/auditService';
import { sendUserNotification, sendRestrictionNotification, sendAdminRestrictionNotification } from '@/utils/notificationService';

// Validation schema for appeal review
const appealReviewSchema = z.object({
  decision: z.enum(['approved', 'denied', 'escalated']),
  adminResponse: z.string().min(10).max(2000),
  internalNotes: z.string().optional(),
  escalationReason: z.string().optional(),
  escalateToAdminId: z.number().optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const appealId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const body = await request.json();

    // Validate input
    const validatedData = appealReviewSchema.parse(body);

    // Get appeal details
    const [appeals] = await db.execute(`
      SELECT 
        a.*,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        u.email as user_email,
        r.restriction_type,
        r.id as restriction_id,
        r.status as restriction_status
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      JOIN user_restrictions r ON a.restriction_id = r.id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(appeals) || appeals.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = appeals[0] as any;

    // Check if appeal can be reviewed
    if (!['submitted', 'under_review', 'escalated'].includes(appeal.status)) {
      return NextResponse.json(
        { error: 'Appeal cannot be reviewed in its current status' },
        { status: 400 }
      );
    }

    // Process the decision
    let newStatus = validatedData.decision;
    let decisionAt = new Date();

    // Handle escalation
    if (validatedData.decision === 'escalated') {
      if (!validatedData.escalationReason) {
        return NextResponse.json(
          { error: 'Escalation reason is required when escalating' },
          { status: 400 }
        );
      }

      // Update appeal with escalation
      await db.execute(`
        UPDATE restriction_appeals
        SET 
          status = 'escalated',
          admin_response = ?,
          internal_notes = ?,
          escalation_reason = ?,
          escalated_to_admin_id = ?,
          reviewed_by_admin_id = ?,
          reviewed_at = NOW(),
          updated_at = NOW()
        WHERE id = ?
      `, [
        validatedData.adminResponse,
        validatedData.internalNotes || null,
        validatedData.escalationReason,
        validatedData.escalateToAdminId || null,
        adminId,
        appealId
      ]);

      // Log the escalation
      await logAdminAction({
        adminId,
        actionType: 'appeal_escalated',
        targetType: 'restriction_appeal',
        targetId: appealId,
        affectedUserId: appeal.user_id,
        actionDetails: {
          escalation_reason: validatedData.escalationReason,
          escalated_to_admin_id: validatedData.escalateToAdminId,
          admin_response: validatedData.adminResponse
        },
        reason: 'Appeal escalated to senior admin',
        severity: 'medium'
      });

      return NextResponse.json({
        success: true,
        message: 'Appeal escalated successfully',
        appeal: {
          id: appealId,
          status: 'escalated',
          reviewed_at: new Date().toISOString()
        }
      });
    }

    // Handle approval or denial
    await db.execute(`
      UPDATE restriction_appeals
      SET 
        status = ?,
        admin_response = ?,
        internal_notes = ?,
        reviewed_by_admin_id = ?,
        reviewed_at = NOW(),
        decision_at = NOW(),
        updated_at = NOW()
      WHERE id = ?
    `, [
      newStatus,
      validatedData.adminResponse,
      validatedData.internalNotes || null,
      adminId,
      appealId
    ]);

    // If approved, lift the restriction
    if (validatedData.decision === 'approved') {
      await db.execute(`
        UPDATE user_restrictions
        SET 
          status = 'lifted',
          lifted_at = NOW(),
          lifted_by_admin_id = ?,
          admin_notes = CONCAT(COALESCE(admin_notes, ''), '\n\nLifted due to approved appeal: ', ?),
          updated_at = NOW()
        WHERE id = ?
      `, [adminId, validatedData.adminResponse, appeal.restriction_id]);

      // Update user restriction status
      await updateUserRestrictionStatus(appeal.user_id);
    }

    // Log the decision
    await logAdminAction({
      adminId,
      actionType: validatedData.decision === 'approved' ? 'appeal_approved' : 'appeal_denied',
      targetType: 'restriction_appeal',
      targetId: appealId,
      affectedUserId: appeal.user_id,
      actionDetails: {
        decision: validatedData.decision,
        admin_response: validatedData.adminResponse,
        restriction_lifted: validatedData.decision === 'approved',
        restriction_id: appeal.restriction_id,
        restriction_type: appeal.restriction_type
      },
      reason: `Appeal ${validatedData.decision}`,
      severity: validatedData.decision === 'approved' ? 'medium' : 'low'
    });

    // Send notification to user with real-time updates
    const notificationTitle = validatedData.decision === 'approved' ?
      'Appeal Approved' : 'Appeal Decision';

    const notificationMessage = validatedData.decision === 'approved' ?
      `Your appeal has been approved and your ${appeal.restriction_type.replace('_', ' ')} restriction has been lifted.` :
      `Your appeal has been reviewed. Please check your restriction status for details.`;

    await sendRestrictionNotification({
      userId: appeal.user_id,
      type: 'appeal_decided',
      title: notificationTitle,
      message: notificationMessage,
      data: {
        decision: validatedData.decision,
        restriction_type: appeal.restriction_type,
        admin_response: validatedData.adminResponse,
        restriction_lifted: validatedData.decision === 'approved'
      },
      restrictionId: appeal.restriction_id,
      appealId,
      severity: validatedData.decision === 'approved' ? 'medium' : 'low'
    });

    // Notify admins of appeal decision
    await sendAdminRestrictionNotification({
      type: 'appeal_decided',
      title: `Appeal ${validatedData.decision.charAt(0).toUpperCase() + validatedData.decision.slice(1)}`,
      message: `Appeal for ${appeal.restriction_type.replace('_', ' ')} restriction has been ${validatedData.decision}`,
      data: {
        decision: validatedData.decision,
        restriction_type: appeal.restriction_type,
        reviewed_by: adminId,
        restriction_lifted: validatedData.decision === 'approved'
      },
      priority: 'low',
      userId: appeal.user_id,
      restrictionId: appeal.restriction_id,
      appealId
    });

    return NextResponse.json({
      success: true,
      message: `Appeal ${validatedData.decision} successfully`,
      appeal: {
        id: appealId,
        status: newStatus,
        decision: validatedData.decision,
        reviewed_at: new Date().toISOString(),
        decision_at: decisionAt.toISOString()
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }

    console.error('Error reviewing appeal:', error);
    return NextResponse.json(
      { error: 'Failed to review appeal' },
      { status: 500 }
    );
  }
}

// Helper function to update user restriction status
async function updateUserRestrictionStatus(userId: number): Promise<void> {
  try {
    // Count active restrictions
    const [activeRestrictions] = await db.execute(`
      SELECT 
        COUNT(*) as count,
        restriction_type,
        severity
      FROM user_restrictions
      WHERE user_id = ? AND status = 'active'
      GROUP BY restriction_type, severity
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 4
          WHEN 'severe' THEN 3
          WHEN 'moderate' THEN 2
          WHEN 'minor' THEN 1
          ELSE 0
        END DESC,
        created_at DESC
    `, [userId]);

    const restrictions = activeRestrictions as any[];
    const restrictionCount = restrictions.reduce((sum, r) => sum + r.count, 0);

    let primaryRestriction = 'none';
    if (restrictionCount > 1) {
      primaryRestriction = 'multiple_restrictions';
    } else if (restrictionCount === 1) {
      primaryRestriction = restrictions[0].restriction_type;
    }

    // Update user table
    await db.execute(`
      UPDATE users
      SET 
        restriction_count = ?,
        restriction_status = ?,
        last_restriction_check = NOW()
      WHERE user_id = ?
    `, [restrictionCount, primaryRestriction, userId]);

  } catch (error) {
    console.error(`Error updating user restriction status for user ${userId}:`, error);
  }
}
