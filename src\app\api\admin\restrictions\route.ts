import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logAdminAction } from '@/services/auditService';
import { sendAdminNotification, sendRestrictionNotification, sendAdminRestrictionNotification } from '@/utils/notificationService';
import { 
  RestrictionType, 
  RestrictionReasonCategory, 
  RestrictionSeverity,
  getRestrictionTypeInfo,
  requiresApproval
} from '@/types/restrictions';

// GET endpoint to fetch all restrictions for admin interface
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const severity = searchParams.get('severity');
    const search = searchParams.get('search');

    let whereConditions = [];
    let queryParams = [];

    // Build WHERE conditions
    if (status && status !== 'all') {
      whereConditions.push('r.status = ?');
      queryParams.push(status);
    }

    if (type && type !== 'all') {
      whereConditions.push('r.restriction_type = ?');
      queryParams.push(type);
    }

    if (severity && severity !== 'all') {
      whereConditions.push('r.severity = ?');
      queryParams.push(severity);
    }

    if (search) {
      whereConditions.push('(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_restrictions r
      JOIN users u ON r.user_id = u.user_id
      ${whereClause}
    `;

    const [countResult] = await db.execute(countQuery, queryParams);
    const totalRestrictions = (countResult as any[])[0]?.total || 0;

    // Get restrictions with pagination
    const offset = (page - 1) * limit;
    const restrictionsQuery = `
      SELECT
        r.*,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name,
        report.id as report_id,
        report.category as report_category
      FROM user_restrictions r
      JOIN users u ON r.user_id = u.user_id
      LEFT JOIN users admin ON r.applied_by_admin_id = admin.user_id
      LEFT JOIN user_reports report ON r.related_report_id = report.id
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(limit, offset);
    const [restrictions] = await db.execute(restrictionsQuery, queryParams);

    // Format the results
    const formattedRestrictions = (restrictions as any[]).map(restriction => ({
      id: restriction.id,
      user_id: restriction.user_id,
      user_name: `${restriction.first_name} ${restriction.last_name}`,
      user_email: restriction.email,
      user_role: restriction.role,
      restriction_type: restriction.restriction_type,
      reason_category: restriction.reason_category,
      custom_reason: restriction.custom_reason,
      status: restriction.status,
      severity: restriction.severity,
      start_date: restriction.start_date,
      end_date: restriction.end_date,
      is_permanent: Boolean(restriction.is_permanent),
      applied_by_admin: restriction.admin_first_name ?
        `${restriction.admin_first_name} ${restriction.admin_last_name}` :
        'System',
      related_report_id: restriction.report_id,
      related_report_category: restriction.report_category,
      admin_notes: restriction.admin_notes,
      user_notified: Boolean(restriction.user_notified),
      appeal_deadline: restriction.appeal_deadline,
      created_at: restriction.created_at,
      updated_at: restriction.updated_at
    }));

    return NextResponse.json({
      restrictions: formattedRestrictions,
      pagination: {
        page,
        limit,
        total: totalRestrictions,
        totalPages: Math.ceil(totalRestrictions / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching restrictions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restrictions' },
      { status: 500 }
    );
  }
}

// Validation schema for restriction application
const restrictionApplicationSchema = z.object({
  userId: z.number().int().positive(),
  restrictionType: z.nativeEnum(RestrictionType),
  reasonCategory: z.nativeEnum(RestrictionReasonCategory),
  customReason: z.string().min(10).max(1000),
  severity: z.nativeEnum(RestrictionSeverity).optional(),
  duration: z.number().int().positive().optional(),
  isPermanent: z.boolean().default(false),
  relatedReportId: z.number().int().positive().optional(),
  adminNotes: z.string().max(2000).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const adminId = parseInt(session.user.id);
    const body = await request.json();

    // Validate input
    const validatedData = restrictionApplicationSchema.parse(body);

    // Verify target user exists
    const [targetUser] = await db.execute(`
      SELECT user_id, first_name, last_name, role, restriction_status
      FROM users WHERE user_id = ?
    `, [validatedData.userId]);

    if (!Array.isArray(targetUser) || targetUser.length === 0) {
      return NextResponse.json(
        { error: 'Target user not found' },
        { status: 404 }
      );
    }

    const user = targetUser[0] as any;

    // Prevent restricting other admins (unless super admin functionality is added)
    if (user.role === 'admin') {
      return NextResponse.json(
        { error: 'Cannot restrict admin users' },
        { status: 403 }
      );
    }

    // Check for existing active restrictions of the same type
    const [existingRestrictions] = await db.execute(`
      SELECT id, restriction_type, status, end_date, is_permanent
      FROM user_restrictions
      WHERE user_id = ? AND restriction_type = ? AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
    `, [validatedData.userId, validatedData.restrictionType]);

    if (Array.isArray(existingRestrictions) && existingRestrictions.length > 0) {
      return NextResponse.json(
        { error: `User already has an active ${validatedData.restrictionType.replace('_', ' ')} restriction` },
        { status: 409 }
      );
    }

    // Calculate end date
    let endDate = null;
    if (!validatedData.isPermanent && validatedData.duration) {
      endDate = new Date();
      endDate.setDate(endDate.getDate() + validatedData.duration);
    }

    // Calculate appeal deadline (7 days from now for most restrictions)
    const appealDeadline = new Date();
    appealDeadline.setDate(appealDeadline.getDate() + 7);

    // Get restriction type info for severity default
    const restrictionInfo = getRestrictionTypeInfo(validatedData.restrictionType);
    const severity = validatedData.severity || RestrictionSeverity.MODERATE;

    // Check if this restriction type requires approval
    const needsApproval = requiresApproval(validatedData.restrictionType);

    // Insert restriction
    const [result] = await db.execute(`
      INSERT INTO user_restrictions (
        user_id, restriction_type, reason_category, custom_reason,
        start_date, end_date, is_permanent, applied_by_admin_id,
        status, severity, related_report_id, admin_notes,
        user_notified, appeal_deadline, created_at
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, FALSE, ?, NOW())
    `, [
      validatedData.userId,
      validatedData.restrictionType,
      validatedData.reasonCategory,
      validatedData.customReason,
      endDate,
      validatedData.isPermanent,
      adminId,
      needsApproval ? 'suspended' : 'active', // Suspended until approved if needed
      severity,
      validatedData.relatedReportId || null,
      validatedData.adminNotes || null,
      appealDeadline
    ]);

    const restrictionId = (result as any).insertId;

    // Update user's restriction status
    await db.execute(`
      UPDATE users 
      SET restriction_status = 'restricted', last_restriction_check = NOW()
      WHERE user_id = ?
    `, [validatedData.userId]);

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType: 'restriction_applied',
        targetType: 'user_restriction',
        targetId: restrictionId,
        affectedUserId: validatedData.userId,
        actionDetails: {
          restrictionType: validatedData.restrictionType,
          reasonCategory: validatedData.reasonCategory,
          customReason: validatedData.customReason,
          severity,
          duration: validatedData.duration,
          isPermanent: validatedData.isPermanent,
          needsApproval
        },
        reason: `Applied ${validatedData.restrictionType.replace('_', ' ')} restriction`,
        severity: severity === RestrictionSeverity.CRITICAL ? 'critical' : 'medium',
        requiresApproval: needsApproval
      });
    } catch (logError) {
      console.error('Failed to log restriction action:', logError);
    }

    // Send notifications
    try {
      if (needsApproval) {
        // Notify senior admins for approval
        await sendAdminRestrictionNotification({
          type: 'restriction_approval_required',
          title: 'Restriction Requires Approval',
          message: `${validatedData.restrictionType.replace('_', ' ')} restriction for ${user.first_name} ${user.last_name} requires approval`,
          data: {
            restrictionType: validatedData.restrictionType,
            appliedBy: adminId,
            severity,
            reason: validatedData.customReason
          },
          priority: 'high',
          userId: validatedData.userId,
          restrictionId
        });
      } else {
        // Notify user of restriction with real-time updates
        await sendRestrictionNotification({
          userId: validatedData.userId,
          type: 'restriction_applied',
          title: 'Account Restriction Applied',
          message: `A ${validatedData.restrictionType.replace('_', ' ')} restriction has been applied to your account.`,
          data: {
            restrictionType: validatedData.restrictionType,
            severity,
            reason: validatedData.customReason,
            appealDeadline: appealDeadline?.toISOString(),
            canAppeal: true
          },
          restrictionId,
          severity: severity === RestrictionSeverity.CRITICAL ? 'critical' : 'medium'
        });

        // Notify admins of new restriction
        await sendAdminRestrictionNotification({
          type: 'restriction_applied',
          title: 'User Restriction Applied',
          message: `${validatedData.restrictionType.replace('_', ' ')} restriction applied to ${user.first_name} ${user.last_name}`,
          data: {
            restrictionType: validatedData.restrictionType,
            appliedBy: adminId,
            severity,
            reason: validatedData.customReason
          },
          priority: severity === RestrictionSeverity.CRITICAL ? 'high' : 'medium',
          userId: validatedData.userId,
          restrictionId
        });
      }
    } catch (notificationError) {
      console.error('Failed to send restriction notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      message: needsApproval ? 
        'Restriction created and pending approval' : 
        'Restriction applied successfully',
      restrictionId,
      needsApproval,
      appealDeadline
    }, { status: 201 });

  } catch (error) {
    console.error('Error applying restriction:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to apply restriction' },
      { status: 500 }
    );
  }
}

