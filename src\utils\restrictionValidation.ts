import { db } from '@/lib/db';

export interface RestrictionCheckResult {
  isAllowed: boolean;
  restrictions: any[];
  blockingRestrictions: any[];
  reason?: string;
}

/**
 * Check if a user is allowed to perform a specific action based on their restrictions
 */
export async function checkUserActionRestriction(
  userId: number, 
  action: string
): Promise<RestrictionCheckResult> {
  try {
    // Get user's active restrictions
    const [restrictions] = await db.execute(`
      SELECT 
        id,
        restriction_type,
        reason_category,
        custom_reason,
        severity,
        status,
        end_date,
        is_permanent
      FROM user_restrictions
      WHERE user_id = ? 
      AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
    `, [userId]);

    if (!Array.isArray(restrictions) || restrictions.length === 0) {
      return {
        isAllowed: true,
        restrictions: [],
        blockingRestrictions: []
      };
    }

    const activeRestrictions = restrictions as any[];

    // Check for account suspension (blocks everything)
    const accountSuspension = activeRestrictions.find(r => 
      r.restriction_type === 'account_suspension'
    );

    if (accountSuspension) {
      return {
        isAllowed: false,
        restrictions: activeRestrictions,
        blockingRestrictions: [accountSuspension],
        reason: 'Account is suspended'
      };
    }

    // Check action-specific restrictions
    const blockingRestrictions = [];

    for (const restriction of activeRestrictions) {
      let isBlocking = false;

      switch (action) {
        case 'booking':
        case 'create_booking':
        case 'cancel_booking':
          if (restriction.restriction_type === 'booking_restrictions') {
            isBlocking = true;
          }
          break;

        case 'payment':
        case 'create_payment':
        case 'process_payment':
          if (restriction.restriction_type === 'payment_restrictions') {
            isBlocking = true;
          }
          break;

        case 'messaging':
        case 'send_message':
        case 'create_notification':
          if (restriction.restriction_type === 'communication_restrictions') {
            isBlocking = true;
          }
          break;

        case 'service_provider':
        case 'provide_service':
        case 'manage_service':
          if (restriction.restriction_type === 'service_provider_restrictions') {
            isBlocking = true;
          }
          break;

        case 'profile_edit':
        case 'update_profile':
          if (restriction.restriction_type === 'profile_restrictions') {
            isBlocking = true;
          }
          break;

        case 'review':
        case 'create_review':
        case 'update_review':
          if (restriction.restriction_type === 'review_restrictions') {
            isBlocking = true;
          }
          break;
      }

      if (isBlocking) {
        blockingRestrictions.push(restriction);
      }
    }

    const isAllowed = blockingRestrictions.length === 0;

    return {
      isAllowed,
      restrictions: activeRestrictions,
      blockingRestrictions,
      reason: blockingRestrictions.length > 0 ? 
        `Action blocked due to ${blockingRestrictions[0].restriction_type.replace('_', ' ')}` : 
        undefined
    };

  } catch (error) {
    console.error('Error checking user action restriction:', error);
    // On error, allow the action (fail open for better UX)
    return {
      isAllowed: true,
      restrictions: [],
      blockingRestrictions: []
    };
  }
}

/**
 * Middleware-style function to check restrictions and return error response if blocked
 */
export async function validateUserAction(
  userId: number, 
  action: string
): Promise<{ allowed: true } | { allowed: false; error: string; status: number; restrictions: any[] }> {
  const result = await checkUserActionRestriction(userId, action);

  if (!result.isAllowed) {
    return {
      allowed: false,
      error: result.reason || 'Action not allowed due to account restrictions',
      status: 403,
      restrictions: result.blockingRestrictions
    };
  }

  return { allowed: true };
}

/**
 * Quick check for account suspension only
 */
export async function isAccountSuspended(userId: number): Promise<boolean> {
  try {
    const [restrictions] = await db.execute(`
      SELECT id
      FROM user_restrictions
      WHERE user_id = ? 
      AND restriction_type = 'account_suspension'
      AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
      LIMIT 1
    `, [userId]);

    return Array.isArray(restrictions) && restrictions.length > 0;
  } catch (error) {
    console.error('Error checking account suspension:', error);
    return false;
  }
}

/**
 * Get user's restriction status for quick lookup
 */
export async function getUserRestrictionStatus(userId: number): Promise<{
  hasRestrictions: boolean;
  restrictionCount: number;
  primaryRestriction: string;
  restrictions: any[];
}> {
  try {
    const [restrictions] = await db.execute(`
      SELECT 
        restriction_type,
        severity,
        status
      FROM user_restrictions
      WHERE user_id = ? 
      AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 4
          WHEN 'severe' THEN 3
          WHEN 'moderate' THEN 2
          WHEN 'minor' THEN 1
          ELSE 0
        END DESC,
        created_at DESC
    `, [userId]);

    const activeRestrictions = restrictions as any[];
    const restrictionCount = activeRestrictions.length;
    
    let primaryRestriction = 'none';
    if (restrictionCount > 1) {
      primaryRestriction = 'multiple_restrictions';
    } else if (restrictionCount === 1) {
      primaryRestriction = activeRestrictions[0].restriction_type;
    }

    return {
      hasRestrictions: restrictionCount > 0,
      restrictionCount,
      primaryRestriction,
      restrictions: activeRestrictions
    };

  } catch (error) {
    console.error('Error getting user restriction status:', error);
    return {
      hasRestrictions: false,
      restrictionCount: 0,
      primaryRestriction: 'none',
      restrictions: []
    };
  }
}
