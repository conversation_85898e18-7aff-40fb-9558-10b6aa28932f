import { db } from '@/lib/db';
import { logAdminAction } from '@/services/auditService';
import { sendUserNotification } from '@/utils/notificationService';

/**
 * Service to handle automatic lifting of expired restrictions
 */
export class RestrictionScheduler {
  private static instance: RestrictionScheduler;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  private constructor() {}

  public static getInstance(): RestrictionScheduler {
    if (!RestrictionScheduler.instance) {
      RestrictionScheduler.instance = new RestrictionScheduler();
    }
    return RestrictionScheduler.instance;
  }

  /**
   * Start the restriction scheduler
   * @param intervalMinutes - How often to check for expired restrictions (default: 5 minutes)
   */
  public start(intervalMinutes: number = 5): void {
    if (this.isRunning) {
      console.log('Restriction scheduler is already running');
      return;
    }

    console.log(`Starting restriction scheduler with ${intervalMinutes} minute intervals`);
    
    // Run immediately on start
    this.processExpiredRestrictions();

    // Set up recurring interval
    this.intervalId = setInterval(() => {
      this.processExpiredRestrictions();
    }, intervalMinutes * 60 * 1000);

    this.isRunning = true;
  }

  /**
   * Stop the restriction scheduler
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('Restriction scheduler stopped');
  }

  /**
   * Check for and process expired restrictions
   */
  public async processExpiredRestrictions(): Promise<void> {
    try {
      console.log('Processing expired restrictions...');

      // Find all expired restrictions that are still active
      const [expiredRestrictions] = await db.execute(`
        SELECT 
          r.*,
          u.first_name,
          u.last_name,
          u.email
        FROM user_restrictions r
        JOIN users u ON r.user_id = u.user_id
        WHERE r.status = 'active'
        AND r.is_permanent = FALSE
        AND r.end_date IS NOT NULL
        AND r.end_date <= NOW()
      `);

      if (!Array.isArray(expiredRestrictions) || expiredRestrictions.length === 0) {
        console.log('No expired restrictions found');
        return;
      }

      console.log(`Found ${expiredRestrictions.length} expired restrictions to process`);

      for (const restriction of expiredRestrictions as any[]) {
        await this.liftExpiredRestriction(restriction);
      }

      // Update user restriction statuses after processing
      await this.updateUserRestrictionStatuses();

      console.log('Finished processing expired restrictions');

    } catch (error) {
      console.error('Error processing expired restrictions:', error);
    }
  }

  /**
   * Lift a single expired restriction
   */
  private async liftExpiredRestriction(restriction: any): Promise<void> {
    try {
      // Update restriction status to expired
      await db.execute(`
        UPDATE user_restrictions
        SET 
          status = 'expired',
          lifted_at = NOW(),
          lifted_by_admin_id = NULL,
          updated_at = NOW()
        WHERE id = ?
      `, [restriction.id]);

      // Log the automatic lifting
      await logAdminAction({
        adminId: null, // System action
        actionType: 'restriction_lifted',
        targetType: 'user_restriction',
        targetId: restriction.id,
        affectedUserId: restriction.user_id,
        actionDetails: {
          restriction_type: restriction.restriction_type,
          reason: 'Automatic expiration',
          original_end_date: restriction.end_date,
          lifted_automatically: true
        },
        reason: 'Restriction expired automatically',
        severity: 'low'
      });

      // Send notification to user
      await sendUserNotification({
        userId: restriction.user_id,
        type: 'restriction_lifted',
        title: 'Restriction Lifted',
        message: `Your ${restriction.restriction_type.replace('_', ' ')} restriction has been automatically lifted as it has expired.`,
        data: {
          restriction_id: restriction.id,
          restriction_type: restriction.restriction_type,
          lifted_automatically: true
        }
      });

      console.log(`Lifted expired restriction ${restriction.id} for user ${restriction.user_id}`);

    } catch (error) {
      console.error(`Error lifting restriction ${restriction.id}:`, error);
    }
  }

  /**
   * Update user restriction statuses after processing
   */
  private async updateUserRestrictionStatuses(): Promise<void> {
    try {
      // Get all users who had restrictions processed
      const [usersWithChanges] = await db.execute(`
        SELECT DISTINCT user_id
        FROM user_restrictions
        WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `);

      for (const user of usersWithChanges as any[]) {
        await this.refreshUserRestrictionStatus(user.user_id);
      }

    } catch (error) {
      console.error('Error updating user restriction statuses:', error);
    }
  }

  /**
   * Refresh restriction status for a specific user
   */
  private async refreshUserRestrictionStatus(userId: number): Promise<void> {
    try {
      // Count active restrictions
      const [activeRestrictions] = await db.execute(`
        SELECT 
          COUNT(*) as count,
          restriction_type,
          severity
        FROM user_restrictions
        WHERE user_id = ? AND status = 'active'
        GROUP BY restriction_type, severity
        ORDER BY 
          CASE severity 
            WHEN 'critical' THEN 4
            WHEN 'severe' THEN 3
            WHEN 'moderate' THEN 2
            WHEN 'minor' THEN 1
            ELSE 0
          END DESC,
          created_at DESC
      `, [userId]);

      const restrictions = activeRestrictions as any[];
      const restrictionCount = restrictions.reduce((sum, r) => sum + r.count, 0);

      let primaryRestriction = 'none';
      if (restrictionCount > 1) {
        primaryRestriction = 'multiple_restrictions';
      } else if (restrictionCount === 1) {
        primaryRestriction = restrictions[0].restriction_type;
      }

      // Update user table
      await db.execute(`
        UPDATE users
        SET 
          restriction_count = ?,
          restriction_status = ?,
          last_restriction_check = NOW()
        WHERE user_id = ?
      `, [restrictionCount, primaryRestriction, userId]);

    } catch (error) {
      console.error(`Error refreshing restriction status for user ${userId}:`, error);
    }
  }

  /**
   * Manually trigger processing (useful for testing or immediate execution)
   */
  public async triggerProcessing(): Promise<void> {
    await this.processExpiredRestrictions();
  }

  /**
   * Get scheduler status
   */
  public getStatus(): { isRunning: boolean; intervalId: NodeJS.Timeout | null } {
    return {
      isRunning: this.isRunning,
      intervalId: this.intervalId
    };
  }
}

// Export singleton instance
export const restrictionScheduler = RestrictionScheduler.getInstance();

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  restrictionScheduler.start(5); // Check every 5 minutes in production
} else if (process.env.NODE_ENV === 'development') {
  restrictionScheduler.start(1); // Check every minute in development for testing
}
