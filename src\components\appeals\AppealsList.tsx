'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface Appeal {
  id: number;
  restriction_id: number;
  restriction_type: string;
  reason_category: string;
  appeal_reason: string;
  supporting_documents: string[];
  status: string;
  priority: string;
  submitted_at: string;
  reviewed_at: string | null;
  decision_at: string | null;
  admin_response: string | null;
  internal_notes: string | null;
  escalation_reason: string | null;
  appeal_deadline: string | null;
  response_deadline: string | null;
  assigned_admin: string | null;
  created_at: string;
  updated_at: string;
}

interface AppealsListProps {
  onAppealSelect?: (appeal: Appeal) => void;
}

export default function AppealsList({ onAppealSelect }: AppealsListProps) {
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAppeal, setSelectedAppeal] = useState<Appeal | null>(null);

  useEffect(() => {
    fetchAppeals();
  }, []);

  const fetchAppeals = async () => {
    try {
      const response = await fetch('/api/user/appeals');
      if (!response.ok) throw new Error('Failed to fetch appeals');
      
      const data = await response.json();
      setAppeals(data.appeals || []);
    } catch (error) {
      console.error('Error fetching appeals:', error);
      toast.error('Failed to load appeals');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'under_review':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'denied':
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case 'escalated':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'text-blue-600 bg-blue-100';
      case 'under_review': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'denied': return 'text-red-600 bg-red-100';
      case 'escalated': return 'text-orange-600 bg-orange-100';
      case 'withdrawn': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'urgent': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatRestrictionType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl shadow-md p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (appeals.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-md p-8 text-center">
        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Appeals Submitted</h3>
        <p className="text-gray-600">
          You haven't submitted any appeals yet. If you have an active restriction, 
          you can submit an appeal from your restriction status page.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {appeals.map((appeal) => (
        <div key={appeal.id} className="bg-white rounded-xl shadow-md p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              {getStatusIcon(appeal.status)}
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Appeal for {formatRestrictionType(appeal.restriction_type)}
                </h3>
                <p className="text-sm text-gray-600">
                  Submitted {formatDate(appeal.submitted_at)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(appeal.status)}`}>
                {appeal.status.replace('_', ' ')}
              </span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(appeal.priority)}`}>
                {appeal.priority}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium text-gray-700">Reason Category</p>
              <p className="text-sm text-gray-600">{appeal.reason_category.replace('_', ' ')}</p>
            </div>
            {appeal.response_deadline && (
              <div>
                <p className="text-sm font-medium text-gray-700">Response Deadline</p>
                <p className="text-sm text-gray-600">
                  {formatDate(appeal.response_deadline)}
                </p>
              </div>
            )}
            {appeal.assigned_admin && (
              <div>
                <p className="text-sm font-medium text-gray-700">Assigned Admin</p>
                <p className="text-sm text-gray-600">{appeal.assigned_admin}</p>
              </div>
            )}
            {appeal.supporting_documents.length > 0 && (
              <div>
                <p className="text-sm font-medium text-gray-700">Supporting Documents</p>
                <p className="text-sm text-gray-600">
                  {appeal.supporting_documents.length} file(s) attached
                </p>
              </div>
            )}
          </div>

          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Appeal Reason</p>
            <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
              {appeal.appeal_reason}
            </p>
          </div>

          {appeal.admin_response && (
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Admin Response</p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">{appeal.admin_response}</p>
                {appeal.reviewed_at && (
                  <p className="text-xs text-blue-600 mt-2">
                    Reviewed on {formatDate(appeal.reviewed_at)}
                  </p>
                )}
              </div>
            </div>
          )}

          {appeal.escalation_reason && (
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Escalation Reason</p>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <p className="text-sm text-orange-800">{appeal.escalation_reason}</p>
              </div>
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              Appeal ID: {appeal.id} • Restriction ID: {appeal.restriction_id}
            </div>
            <button
              onClick={() => {
                setSelectedAppeal(appeal);
                onAppealSelect?.(appeal);
              }}
              className="flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm"
            >
              <EyeIcon className="h-4 w-4" />
              View Details
            </button>
          </div>
        </div>
      ))}

      {/* Appeal Details Modal */}
      {selectedAppeal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Appeal Details</h3>
                <button
                  onClick={() => setSelectedAppeal(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Status:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(selectedAppeal.status)}`}>
                      {selectedAppeal.status.replace('_', ' ')}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Priority:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getPriorityColor(selectedAppeal.priority)}`}>
                      {selectedAppeal.priority}
                    </span>
                  </div>
                </div>

                <div>
                  <p className="font-medium text-gray-700 mb-2">Full Appeal Reason</p>
                  <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-600">
                    {selectedAppeal.appeal_reason}
                  </div>
                </div>

                {selectedAppeal.supporting_documents.length > 0 && (
                  <div>
                    <p className="font-medium text-gray-700 mb-2">Supporting Documents</p>
                    <div className="space-y-2">
                      {selectedAppeal.supporting_documents.map((doc, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <DocumentTextIcon className="h-4 w-4 text-gray-500" />
                          <a 
                            href={doc} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800"
                          >
                            Document {index + 1}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedAppeal.admin_response && (
                  <div>
                    <p className="font-medium text-gray-700 mb-2">Admin Response</p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800">{selectedAppeal.admin_response}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
