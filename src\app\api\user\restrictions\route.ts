import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);

    // Get user's restrictions
    const [restrictions] = await db.execute(`
      SELECT 
        r.*,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM user_restrictions r
      LEFT JOIN users admin ON r.applied_by_admin_id = admin.user_id
      WHERE r.user_id = ?
      ORDER BY r.created_at DESC
    `, [userId]);

    // Format the restrictions data
    const formattedRestrictions = (restrictions as any[]).map(restriction => ({
      id: restriction.id,
      restriction_type: restriction.restriction_type,
      reason_category: restriction.reason_category,
      custom_reason: restriction.custom_reason,
      start_date: restriction.start_date,
      end_date: restriction.end_date,
      is_permanent: Boolean(restriction.is_permanent),
      status: restriction.status,
      severity: restriction.severity,
      admin_notes: restriction.admin_notes,
      user_notified: Boolean(restriction.user_notified),
      appeal_deadline: restriction.appeal_deadline,
      created_at: restriction.created_at,
      updated_at: restriction.updated_at,
      applied_by_admin: restriction.admin_first_name ? 
        `${restriction.admin_first_name} ${restriction.admin_last_name}` : 
        'System'
    }));

    // Check if user has any active restrictions
    const hasActiveRestrictions = formattedRestrictions.some(r => r.status === 'active');

    // Update user's restriction status if needed
    if (hasActiveRestrictions) {
      await db.execute(`
        UPDATE users 
        SET restriction_status = 'restricted', last_restriction_check = NOW()
        WHERE user_id = ?
      `, [userId]);
    } else {
      await db.execute(`
        UPDATE users 
        SET restriction_status = 'none', last_restriction_check = NOW()
        WHERE user_id = ?
      `, [userId]);
    }

    return NextResponse.json({
      restrictions: formattedRestrictions,
      hasActiveRestrictions,
      totalRestrictions: formattedRestrictions.length
    });

  } catch (error) {
    console.error('Error fetching user restrictions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restrictions' },
      { status: 500 }
    );
  }
}

// Check if user has specific restriction type or if an action is allowed
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { restrictionType, action, route } = body;
    const userId = parseInt(session.user.id);

    // If checking specific restriction type (legacy behavior)
    if (restrictionType && !action && !route) {
      const [restrictions] = await db.execute(`
        SELECT id, restriction_type, end_date, is_permanent, status
        FROM user_restrictions
        WHERE user_id = ? AND restriction_type = ? AND status = 'active'
        AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
      `, [userId, restrictionType]);

      const hasRestriction = Array.isArray(restrictions) && restrictions.length > 0;
      const restrictionData = hasRestriction ? restrictions[0] : null;

      return NextResponse.json({
        hasRestriction,
        restriction: restrictionData,
        message: hasRestriction ?
          `You currently have an active ${restrictionType.replace('_', ' ')} restriction` :
          `No active ${restrictionType.replace('_', ' ')} restriction found`
      });
    }

    // Enhanced action checking
    const [restrictions] = await db.execute(`
      SELECT
        r.*,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM user_restrictions r
      LEFT JOIN users admin ON r.applied_by_admin_id = admin.user_id
      WHERE r.user_id = ?
      AND r.status = 'active'
      AND (r.end_date IS NULL OR r.end_date > NOW() OR r.is_permanent = TRUE)
      ORDER BY r.created_at DESC
    `, [userId]);

    if (!Array.isArray(restrictions)) {
      return NextResponse.json(
        { error: 'Failed to fetch restrictions' },
        { status: 500 }
      );
    }

    const activeRestrictions = restrictions as any[];

    // Check for account suspension (blocks everything)
    const accountSuspension = activeRestrictions.find(r =>
      r.restriction_type === 'account_suspension'
    );

    if (accountSuspension) {
      return NextResponse.json({
        allowed: false,
        blocked: true,
        reason: 'Account suspended',
        restriction: {
          type: 'account_suspension',
          reason: accountSuspension.custom_reason,
          severity: accountSuspension.severity,
          end_date: accountSuspension.end_date,
          is_permanent: accountSuspension.is_permanent
        },
        restrictions: activeRestrictions
      });
    }

    // Check action-specific restrictions
    const blockingRestrictions = [];

    for (const restriction of activeRestrictions) {
      let isBlocking = false;

      switch (restriction.restriction_type) {
        case 'booking_restrictions':
          if (action === 'booking' || route?.includes('/bookings') || route?.includes('/book-service')) {
            isBlocking = true;
          }
          break;

        case 'service_provider_restrictions':
          if (action === 'service_provider' || route?.includes('/services') || route?.includes('/provider')) {
            isBlocking = true;
          }
          break;

        case 'communication_restrictions':
          if (action === 'messaging' || route?.includes('/messages') || route?.includes('/chat')) {
            isBlocking = true;
          }
          break;

        case 'payment_restrictions':
          if (action === 'payment' || route?.includes('/payments') || route?.includes('/billing')) {
            isBlocking = true;
          }
          break;

        case 'profile_restrictions':
          if (action === 'profile_edit' || route?.includes('/profile/edit')) {
            isBlocking = true;
          }
          break;

        case 'review_restrictions':
          if (action === 'review' || route?.includes('/reviews')) {
            isBlocking = true;
          }
          break;
      }

      if (isBlocking) {
        blockingRestrictions.push(restriction);
      }
    }

    const isAllowed = blockingRestrictions.length === 0;

    return NextResponse.json({
      allowed: isAllowed,
      blocked: !isAllowed,
      restrictions: activeRestrictions,
      blockingRestrictions,
      reason: blockingRestrictions.length > 0 ?
        `Action blocked due to ${blockingRestrictions[0].restriction_type}` :
        null
    });

  } catch (error) {
    console.error('Error checking user restriction:', error);
    return NextResponse.json(
      { error: 'Failed to check restriction' },
      { status: 500 }
    );
  }
}
