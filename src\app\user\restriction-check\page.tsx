'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getAuthToken } from '@/utils/auth';

export default function RestrictionCheckPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get('redirect') || '/user/dashboard';

  useEffect(() => {
    const checkRestrictions = async () => {
      try {
        const authToken = getAuthToken();
        if (!authToken) {
          router.push('/');
          return;
        }

        // Check user restrictions via API
        const response = await fetch('/api/user/restrictions', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If API fails, allow access (fail open for better UX)
          router.push(redirectPath);
          return;
        }

        const data = await response.json();
        
        // Check if user has active restrictions that would block this route
        if (data.restrictions && data.restrictions.length > 0) {
          const hasBlockingRestriction = data.restrictions.some((restriction: any) => {
            // Account suspension blocks everything
            if (restriction.restriction_type === 'account_suspension') {
              return true;
            }

            // Check route-specific restrictions
            if (redirectPath.includes('/bookings') && restriction.restriction_type === 'booking_restrictions') {
              return true;
            }
            if (redirectPath.includes('/payments') && restriction.restriction_type === 'payment_restrictions') {
              return true;
            }
            if (redirectPath.includes('/messages') && restriction.restriction_type === 'communication_restrictions') {
              return true;
            }
            if (redirectPath.includes('/services') && restriction.restriction_type === 'service_provider_restrictions') {
              return true;
            }

            return false;
          });

          if (hasBlockingRestriction) {
            // Redirect to restriction status page
            router.push(`/user/restriction-status?blocked_route=${encodeURIComponent(redirectPath)}`);
            return;
          }
        }

        // No blocking restrictions, proceed to original destination
        router.push(redirectPath);

      } catch (error) {
        console.error('Error checking restrictions:', error);
        // On error, allow access (fail open)
        router.push(redirectPath);
      }
    };

    checkRestrictions();
  }, [router, redirectPath]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Checking Account Status
          </h2>
          <p className="text-gray-600">
            Please wait while we verify your account permissions...
          </p>
        </div>
      </div>
    </div>
  );
}
