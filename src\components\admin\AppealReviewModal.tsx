'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import AppealMessaging from '@/components/appeals/AppealMessaging';

interface Appeal {
  id: number;
  restrictionId: number;
  user: {
    id: number;
    name: string;
    email: string;
  };
  restriction: {
    type: string;
    reasonCategory: string;
    severity: string;
  };
  appealReason: string;
  status: string;
  priority: string;
  assignedAdmin: {
    id: number;
    name: string;
  } | null;
  adminResponse: string | null;
  submittedAt: string;
  reviewedAt: string | null;
  responseDeadline: string | null;
}

interface AppealReviewModalProps {
  appeal: Appeal;
  onClose: () => void;
  onReviewComplete: () => void;
}

export default function AppealReviewModal({ 
  appeal, 
  onClose, 
  onReviewComplete 
}: AppealReviewModalProps) {
  const [reviewData, setReviewData] = useState({
    decision: '',
    adminResponse: '',
    internalNotes: '',
    escalationReason: '',
    escalateToAdminId: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showMessaging, setShowMessaging] = useState(false);
  const [currentAdminId, setCurrentAdminId] = useState<number | null>(null);

  useEffect(() => {
    // Get current admin ID from session or context
    // This would typically come from your auth context
    setCurrentAdminId(1); // Placeholder - replace with actual admin ID
  }, []);

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reviewData.decision) {
      toast.error('Please select a decision');
      return;
    }

    if (reviewData.adminResponse.trim().length < 10) {
      toast.error('Admin response must be at least 10 characters long');
      return;
    }

    if (reviewData.decision === 'escalated' && !reviewData.escalationReason.trim()) {
      toast.error('Escalation reason is required when escalating');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload: any = {
        decision: reviewData.decision,
        adminResponse: reviewData.adminResponse,
        internalNotes: reviewData.internalNotes || undefined
      };

      if (reviewData.decision === 'escalated') {
        payload.escalationReason = reviewData.escalationReason;
        if (reviewData.escalateToAdminId) {
          payload.escalateToAdminId = parseInt(reviewData.escalateToAdminId);
        }
      }

      const response = await fetch(`/api/admin/appeals/${appeal.id}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to submit review');
      }

      toast.success(`Appeal ${reviewData.decision} successfully`);
      onReviewComplete();
      onClose();
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatRestrictionType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor': return 'text-yellow-600 bg-yellow-100';
      case 'moderate': return 'text-orange-600 bg-orange-100';
      case 'severe': return 'text-red-600 bg-red-100';
      case 'critical': return 'text-red-800 bg-red-200';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const canReview = ['submitted', 'under_review', 'escalated'].includes(appeal.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Appeal Review</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex space-x-4">
            <button
              onClick={() => setShowMessaging(false)}
              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                !showMessaging
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Review Details
            </button>
            <button
              onClick={() => setShowMessaging(true)}
              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                showMessaging
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Communication
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {showMessaging ? (
            /* Messaging Tab */
            <div>
              {currentAdminId && (
                <AppealMessaging
                  appealId={appeal.id}
                  currentUserRole="admin"
                  currentUserId={currentAdminId}
                />
              )}
            </div>
          ) : (
            /* Review Details Tab */
            <div className="space-y-6">
              {/* Appeal Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                User Information
              </h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Name:</span>
                  <span className="ml-2">{appeal.user.name}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Email:</span>
                  <span className="ml-2">{appeal.user.email}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">User ID:</span>
                  <span className="ml-2">{appeal.user.id}</span>
                </div>
              </div>
            </div>

            {/* Restriction Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <ExclamationTriangleIcon className="h-5 w-5" />
                Restriction Details
              </h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Type:</span>
                  <span className="ml-2">{formatRestrictionType(appeal.restriction.type)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Category:</span>
                  <span className="ml-2">{appeal.restriction.reasonCategory.replace('_', ' ')}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Severity:</span>
                  <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(appeal.restriction.severity)}`}>
                    {appeal.restriction.severity}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Restriction ID:</span>
                  <span className="ml-2">{appeal.restrictionId}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Appeal Details */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <DocumentTextIcon className="h-5 w-5" />
              Appeal Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Submitted:</span>
                <span className="ml-2">{formatDate(appeal.submittedAt)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Priority:</span>
                <span className="ml-2 capitalize">{appeal.priority}</span>
              </div>
              {appeal.responseDeadline && (
                <div>
                  <span className="font-medium text-gray-700">Deadline:</span>
                  <span className="ml-2">{formatDate(appeal.responseDeadline)}</span>
                </div>
              )}
            </div>
            <div>
              <p className="font-medium text-gray-700 mb-2">Appeal Reason:</p>
              <div className="bg-white rounded-lg p-3 text-sm text-gray-600">
                {appeal.appealReason}
              </div>
            </div>
          </div>

          {/* Existing Admin Response (if any) */}
          {appeal.adminResponse && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Previous Admin Response</h4>
              <p className="text-sm text-gray-600">{appeal.adminResponse}</p>
              {appeal.reviewedAt && (
                <p className="text-xs text-gray-500 mt-2">
                  Reviewed on {formatDate(appeal.reviewedAt)}
                </p>
              )}
            </div>
          )}

          {/* Review Form */}
          {canReview && (
            <form onSubmit={handleSubmitReview} className="space-y-4">
              <div className="border-t border-gray-200 pt-6">
                <h4 className="font-medium text-gray-900 mb-4">Review Decision</h4>
                
                {/* Decision Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="decision"
                      value="approved"
                      checked={reviewData.decision === 'approved'}
                      onChange={(e) => setReviewData({...reviewData, decision: e.target.value})}
                      className="text-green-600 focus:ring-green-500"
                    />
                    <CheckCircleIcon className="h-5 w-5 text-green-600 ml-2 mr-2" />
                    <span className="text-sm font-medium">Approve Appeal</span>
                  </label>

                  <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="decision"
                      value="denied"
                      checked={reviewData.decision === 'denied'}
                      onChange={(e) => setReviewData({...reviewData, decision: e.target.value})}
                      className="text-red-600 focus:ring-red-500"
                    />
                    <XCircleIcon className="h-5 w-5 text-red-600 ml-2 mr-2" />
                    <span className="text-sm font-medium">Deny Appeal</span>
                  </label>

                  <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="decision"
                      value="escalated"
                      checked={reviewData.decision === 'escalated'}
                      onChange={(e) => setReviewData({...reviewData, decision: e.target.value})}
                      className="text-orange-600 focus:ring-orange-500"
                    />
                    <ExclamationTriangleIcon className="h-5 w-5 text-orange-600 ml-2 mr-2" />
                    <span className="text-sm font-medium">Escalate</span>
                  </label>
                </div>

                {/* Admin Response */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Response *
                  </label>
                  <textarea
                    value={reviewData.adminResponse}
                    onChange={(e) => setReviewData({...reviewData, adminResponse: e.target.value})}
                    required
                    rows={4}
                    placeholder="Provide a detailed response to the user explaining your decision..."
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Escalation Reason (if escalating) */}
                {reviewData.decision === 'escalated' && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Escalation Reason *
                    </label>
                    <textarea
                      value={reviewData.escalationReason}
                      onChange={(e) => setReviewData({...reviewData, escalationReason: e.target.value})}
                      required
                      rows={3}
                      placeholder="Explain why this appeal needs to be escalated..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                )}

                {/* Internal Notes */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Internal Notes (Optional)
                  </label>
                  <textarea
                    value={reviewData.internalNotes}
                    onChange={(e) => setReviewData({...reviewData, internalNotes: e.target.value})}
                    rows={2}
                    placeholder="Internal notes for other admins (not visible to user)..."
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Submit Buttons */}
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !reviewData.decision || reviewData.adminResponse.trim().length < 10}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Review'}
                  </button>
                </div>
              </div>
            </form>
          )}

          {/* Status Message for Non-Reviewable Appeals */}
          {!canReview && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
              <ClockIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">
                This appeal has already been reviewed and cannot be modified.
              </p>
            </div>
          )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
