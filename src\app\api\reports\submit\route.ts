import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { sendAdminNotification } from '@/utils/notificationService';
import { logAdminAction } from '@/services/auditService';

// Validation schema for report submission
const reportSubmissionSchema = z.object({
  reportedUserId: z.number().int().positive(),
  category: z.enum([
    'inappropriate_behavior',
    'spam',
    'fraudulent_activity',
    'service_quality_issues',
    'payment_disputes',
    'harassment',
    'fake_profiles',
    'other_violations'
  ]),
  description: z.string().min(10).max(2000),
  bookingId: z.number().int().positive().optional(),
  evidenceFiles: z.array(z.string()).optional()
});

// File upload validation
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'text/plain'
];

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const reporterId = parseInt(session.user.id);

    // Parse form data for file uploads
    const formData = await request.formData();
    
    // Extract and validate basic report data
    const reportData = {
      reportedUserId: parseInt(formData.get('reportedUserId') as string),
      category: formData.get('category') as string,
      description: formData.get('description') as string,
      bookingId: formData.get('bookingId') ? parseInt(formData.get('bookingId') as string) : undefined
    };

    // Validate report data
    const validatedData = reportSubmissionSchema.parse(reportData);

    // Check if user is trying to report themselves
    if (reporterId === validatedData.reportedUserId) {
      return NextResponse.json(
        { error: 'You cannot report yourself' },
        { status: 400 }
      );
    }

    // Verify reported user exists
    const [reportedUser] = await db.execute(
      'SELECT user_id, first_name, last_name FROM users WHERE user_id = ?',
      [validatedData.reportedUserId]
    );

    if (!Array.isArray(reportedUser) || reportedUser.length === 0) {
      return NextResponse.json(
        { error: 'Reported user not found' },
        { status: 404 }
      );
    }

    // Verify booking exists and involves both users (if booking ID provided)
    if (validatedData.bookingId) {
      const [booking] = await db.execute(`
        SELECT id, user_id, provider_id 
        FROM service_bookings 
        WHERE id = ? AND (user_id = ? OR provider_id = ?)
      `, [validatedData.bookingId, reporterId, reporterId]);

      if (!Array.isArray(booking) || booking.length === 0) {
        return NextResponse.json(
          { error: 'Booking not found or you are not involved in this booking' },
          { status: 404 }
        );
      }

      // Verify the reported user is involved in the booking
      const bookingData = booking[0] as any;
      if (bookingData.user_id !== validatedData.reportedUserId && 
          bookingData.provider_id !== validatedData.reportedUserId) {
        return NextResponse.json(
          { error: 'Reported user is not involved in the specified booking' },
          { status: 400 }
        );
      }
    }

    // Handle file uploads
    const evidenceFiles: string[] = [];
    const files = formData.getAll('evidenceFiles') as File[];

    if (files && files.length > 0) {
      // Create uploads directory if it doesn't exist
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'reports');
      await mkdir(uploadsDir, { recursive: true });

      for (const file of files) {
        if (file.size === 0) continue; // Skip empty files

        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
          return NextResponse.json(
            { error: `File ${file.name} is too large. Maximum size is 10MB.` },
            { status: 400 }
          );
        }

        // Validate file type
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          return NextResponse.json(
            { error: `File type ${file.type} is not allowed.` },
            { status: 400 }
          );
        }

        // Generate unique filename
        const fileExtension = path.extname(file.name);
        const uniqueFilename = `${uuidv4()}${fileExtension}`;
        const filePath = path.join(uploadsDir, uniqueFilename);

        // Save file
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // Store relative path for database
        evidenceFiles.push(`/uploads/reports/${uniqueFilename}`);
      }
    }

    // Check for duplicate reports (same reporter, reported user, and category within 24 hours)
    const [existingReport] = await db.execute(`
      SELECT id FROM user_reports 
      WHERE reporter_id = ? AND reported_user_id = ? AND category = ? 
      AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `, [reporterId, validatedData.reportedUserId, validatedData.category]);

    if (Array.isArray(existingReport) && existingReport.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted a similar report in the last 24 hours' },
        { status: 409 }
      );
    }

    // Insert report into database
    const [result] = await db.execute(`
      INSERT INTO user_reports (
        reporter_id, reported_user_id, category, description, 
        evidence_files, booking_id, status, priority, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'pending', 'medium', NOW())
    `, [
      reporterId,
      validatedData.reportedUserId,
      validatedData.category,
      validatedData.description,
      evidenceFiles.length > 0 ? JSON.stringify(evidenceFiles) : null,
      validatedData.bookingId || null
    ]);

    const reportId = (result as any).insertId;

    // Send notification to admins
    try {
      await sendAdminNotification({
        type: 'report_submitted',
        title: 'New User Report Submitted',
        message: `A new ${validatedData.category.replace('_', ' ')} report has been submitted against ${reportedUser[0].first_name} ${reportedUser[0].last_name}`,
        data: {
          reportId,
          category: validatedData.category,
          reportedUserId: validatedData.reportedUserId,
          reporterId
        }
      });
    } catch (notificationError) {
      console.error('Failed to send admin notification:', notificationError);
      // Don't fail the request if notification fails
    }

    // Log the action
    try {
      await logAdminAction({
        adminId: reporterId, // Reporter is acting as the initiator
        actionType: 'report_created',
        targetType: 'user_report',
        targetId: reportId,
        affectedUserId: validatedData.reportedUserId,
        actionDetails: {
          category: validatedData.category,
          hasEvidence: evidenceFiles.length > 0,
          bookingId: validatedData.bookingId
        },
        reason: `User report: ${validatedData.category}`
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Report submitted successfully. Our admin team will review it shortly.',
      reportId
    }, { status: 201 });

  } catch (error) {
    console.error('Error submitting report:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to submit report. Please try again.' },
      { status: 500 }
    );
  }
}
