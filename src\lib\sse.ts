/**
 * SSE (Server-Sent Events) utility functions
 * 
 * This module provides a centralized way to access SSE broadcasting functions
 * from the SSE route handler. It handles dynamic imports to avoid SSR issues.
 */

// Type definitions for SSE functions
export type BroadcastToAdminsFunction = (notification: any) => void;
export type BroadcastToUserFunction = (userId: string, accountType: string, notification: any) => void;
export type BroadcastToAllFunction = (notification: any) => void;
export type BroadcastRestrictionUpdateFunction = (data: {
  type: 'restriction_applied' | 'restriction_lifted' | 'restriction_modified' | 'appeal_submitted' | 'appeal_decided';
  userId: number;
  restrictionId?: number;
  appealId?: number;
  message: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}) => void;

// Cache for dynamically imported functions
let sseModule: any = null;
let importPromise: Promise<any> | null = null;

/**
 * Dynamically import SSE functions to avoid SSR issues
 */
async function importSSEModule() {
  if (sseModule) {
    return sseModule;
  }

  if (importPromise) {
    return importPromise;
  }

  importPromise = import('../app/api/notifications/sse/route')
    .then(module => {
      sseModule = module;
      return module;
    })
    .catch(error => {
      console.warn('Failed to import SSE module:', error.message);
      importPromise = null;
      return null;
    });

  return importPromise;
}

/**
 * Broadcast notification to all connected admins
 */
export async function broadcastToAdmins(notification: any): Promise<void> {
  try {
    const module = await importSSEModule();
    if (module && typeof module.broadcastToAdmins === 'function') {
      module.broadcastToAdmins(notification);
    }
  } catch (error) {
    console.warn('Failed to broadcast to admins:', error);
  }
}

/**
 * Broadcast notification to specific user
 */
export async function broadcastToUser(userId: string, accountType: string, notification: any): Promise<void> {
  try {
    const module = await importSSEModule();
    if (module && typeof module.broadcastToUser === 'function') {
      module.broadcastToUser(userId, accountType, notification);
    }
  } catch (error) {
    console.warn('Failed to broadcast to user:', error);
  }
}

/**
 * Broadcast notification to all connected users
 */
export async function broadcastToAll(notification: any): Promise<void> {
  try {
    const module = await importSSEModule();
    if (module && typeof module.broadcastToAll === 'function') {
      module.broadcastToAll(notification);
    }
  } catch (error) {
    console.warn('Failed to broadcast to all users:', error);
  }
}

/**
 * Broadcast restriction-related notifications
 */
export async function broadcastRestrictionUpdate(data: {
  type: 'restriction_applied' | 'restriction_lifted' | 'restriction_modified' | 'appeal_submitted' | 'appeal_decided';
  userId: number;
  restrictionId?: number;
  appealId?: number;
  message: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}): Promise<void> {
  try {
    const module = await importSSEModule();
    if (module && typeof module.broadcastRestrictionUpdate === 'function') {
      module.broadcastRestrictionUpdate(data);
    }
  } catch (error) {
    console.warn('Failed to broadcast restriction update:', error);
  }
}

/**
 * Get the number of active SSE connections
 */
export async function getActiveConnections(): Promise<number> {
  try {
    const module = await importSSEModule();
    if (module && typeof module.getActiveConnections === 'function') {
      return module.getActiveConnections();
    }
    return 0;
  } catch (error) {
    console.warn('Failed to get active connections:', error);
    return 0;
  }
}

/**
 * Synchronous versions for backward compatibility
 * These will attempt to use cached module if available
 */

/**
 * Synchronous broadcast to admins (uses cached module)
 */
export function broadcastToAdminsSync(notification: any): void {
  if (sseModule && typeof sseModule.broadcastToAdmins === 'function') {
    try {
      sseModule.broadcastToAdmins(notification);
    } catch (error) {
      console.warn('Failed to broadcast to admins (sync):', error);
    }
  } else {
    // Fallback to async version
    broadcastToAdmins(notification).catch(error => {
      console.warn('Failed to broadcast to admins (async fallback):', error);
    });
  }
}

/**
 * Synchronous broadcast to user (uses cached module)
 */
export function broadcastToUserSync(userId: string, accountType: string, notification: any): void {
  if (sseModule && typeof sseModule.broadcastToUser === 'function') {
    try {
      sseModule.broadcastToUser(userId, accountType, notification);
    } catch (error) {
      console.warn('Failed to broadcast to user (sync):', error);
    }
  } else {
    // Fallback to async version
    broadcastToUser(userId, accountType, notification).catch(error => {
      console.warn('Failed to broadcast to user (async fallback):', error);
    });
  }
}

/**
 * Initialize SSE module (call this early in server startup)
 */
export async function initializeSSE(): Promise<void> {
  try {
    await importSSEModule();
    console.log('SSE module initialized successfully');
  } catch (error) {
    console.warn('Failed to initialize SSE module:', error);
  }
}

// Note: broadcastToAdmins is already exported as async function above
// For immediate use, use broadcastToAdminsSync
