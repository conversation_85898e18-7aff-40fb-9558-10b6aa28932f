import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { logAdminAction } from '@/services/auditService';
import { sendUserNotification } from '@/utils/notificationService';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const restrictionId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const body = await request.json();
    const { reason } = body;

    if (!reason || reason.trim().length < 10) {
      return NextResponse.json(
        { error: 'Reason for lifting restriction is required (minimum 10 characters)' },
        { status: 400 }
      );
    }

    // Get restriction details
    const [restrictions] = await db.execute(`
      SELECT 
        r.*,
        u.first_name,
        u.last_name,
        u.email
      FROM user_restrictions r
      JOIN users u ON r.user_id = u.user_id
      WHERE r.id = ?
    `, [restrictionId]);

    if (!Array.isArray(restrictions) || restrictions.length === 0) {
      return NextResponse.json(
        { error: 'Restriction not found' },
        { status: 404 }
      );
    }

    const restriction = restrictions[0] as any;

    if (restriction.status !== 'active') {
      return NextResponse.json(
        { error: 'Only active restrictions can be lifted' },
        { status: 400 }
      );
    }

    // Update restriction status
    await db.execute(`
      UPDATE user_restrictions
      SET 
        status = 'lifted',
        lifted_at = NOW(),
        lifted_by_admin_id = ?,
        admin_notes = CONCAT(COALESCE(admin_notes, ''), '\n\nLifted by admin: ', ?),
        updated_at = NOW()
      WHERE id = ?
    `, [adminId, reason, restrictionId]);

    // Update user restriction status
    await updateUserRestrictionStatus(restriction.user_id);

    // Log the admin action
    await logAdminAction({
      adminId,
      actionType: 'restriction_lifted',
      targetType: 'user_restriction',
      targetId: restrictionId,
      affectedUserId: restriction.user_id,
      actionDetails: {
        restriction_type: restriction.restriction_type,
        reason: reason,
        lifted_manually: true,
        original_end_date: restriction.end_date,
        was_permanent: restriction.is_permanent
      },
      reason: reason,
      severity: 'medium'
    });

    // Send notification to user
    await sendUserNotification({
      userId: restriction.user_id,
      type: 'restriction_lifted',
      title: 'Restriction Lifted',
      message: `Your ${restriction.restriction_type.replace('_', ' ')} restriction has been lifted by an administrator.`,
      data: {
        restriction_id: restrictionId,
        restriction_type: restriction.restriction_type,
        lifted_by_admin: true,
        reason: reason
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Restriction lifted successfully',
      restriction: {
        id: restrictionId,
        status: 'lifted',
        lifted_at: new Date().toISOString(),
        lifted_by_admin_id: adminId
      }
    });

  } catch (error) {
    console.error('Error lifting restriction:', error);
    return NextResponse.json(
      { error: 'Failed to lift restriction' },
      { status: 500 }
    );
  }
}

// Helper function to update user restriction status
async function updateUserRestrictionStatus(userId: number): Promise<void> {
  try {
    // Count active restrictions
    const [activeRestrictions] = await db.execute(`
      SELECT 
        COUNT(*) as count,
        restriction_type,
        severity
      FROM user_restrictions
      WHERE user_id = ? AND status = 'active'
      GROUP BY restriction_type, severity
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 4
          WHEN 'severe' THEN 3
          WHEN 'moderate' THEN 2
          WHEN 'minor' THEN 1
          ELSE 0
        END DESC,
        created_at DESC
    `, [userId]);

    const restrictions = activeRestrictions as any[];
    const restrictionCount = restrictions.reduce((sum, r) => sum + r.count, 0);

    let primaryRestriction = 'none';
    if (restrictionCount > 1) {
      primaryRestriction = 'multiple_restrictions';
    } else if (restrictionCount === 1) {
      primaryRestriction = restrictions[0].restriction_type;
    }

    // Update user table
    await db.execute(`
      UPDATE users
      SET 
        restriction_count = ?,
        restriction_status = ?,
        last_restriction_check = NOW()
      WHERE user_id = ?
    `, [restrictionCount, primaryRestriction, userId]);

  } catch (error) {
    console.error(`Error updating user restriction status for user ${userId}:`, error);
  }
}
