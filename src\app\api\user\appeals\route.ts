import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { sendAdminNotification, sendRestrictionNotification, sendAdminRestrictionNotification } from '@/utils/notificationService';
import { logAdminAction } from '@/services/auditService';

// Validation schema for appeal submission
const appealSubmissionSchema = z.object({
  restrictionId: z.number().int().positive(),
  appealReason: z.string().min(10).max(2000),
  supportingDocuments: z.array(z.string()).optional()
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);

    // Get user's appeals
    const [appeals] = await db.execute(`
      SELECT 
        a.*,
        r.restriction_type,
        r.reason_category,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM restriction_appeals a
      JOIN user_restrictions r ON a.restriction_id = r.id
      LEFT JOIN users admin ON a.assigned_admin_id = admin.user_id
      WHERE a.user_id = ?
      ORDER BY a.submitted_at DESC
    `, [userId]);

    // Format the appeals data
    const formattedAppeals = (appeals as any[]).map(appeal => ({
      id: appeal.id,
      restriction_id: appeal.restriction_id,
      restriction_type: appeal.restriction_type,
      reason_category: appeal.reason_category,
      appeal_reason: appeal.appeal_reason,
      supporting_documents: appeal.supporting_documents ? JSON.parse(appeal.supporting_documents) : [],
      status: appeal.status,
      priority: appeal.priority,
      submitted_at: appeal.submitted_at,
      reviewed_at: appeal.reviewed_at,
      decision_at: appeal.decision_at,
      admin_response: appeal.admin_response,
      internal_notes: appeal.internal_notes,
      escalation_reason: appeal.escalation_reason,
      appeal_deadline: appeal.appeal_deadline,
      response_deadline: appeal.response_deadline,
      assigned_admin: appeal.admin_first_name ?
        `${appeal.admin_first_name} ${appeal.admin_last_name}` :
        null,
      created_at: appeal.created_at,
      updated_at: appeal.updated_at
    }));

    return NextResponse.json({
      appeals: formattedAppeals,
      totalAppeals: formattedAppeals.length
    });

  } catch (error) {
    console.error('Error fetching user appeals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch appeals' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);
    const body = await request.json();

    // Validate input
    const validatedData = appealSubmissionSchema.parse(body);

    // Verify the restriction exists and belongs to the user
    const [restriction] = await db.execute(`
      SELECT id, user_id, restriction_type, status, appeal_deadline, is_permanent
      FROM user_restrictions
      WHERE id = ? AND user_id = ?
    `, [validatedData.restrictionId, userId]);

    if (!Array.isArray(restriction) || restriction.length === 0) {
      return NextResponse.json(
        { error: 'Restriction not found or does not belong to you' },
        { status: 404 }
      );
    }

    const restrictionData = restriction[0] as any;

    // Check if restriction is still active
    if (restrictionData.status !== 'active') {
      return NextResponse.json(
        { error: 'You can only appeal active restrictions' },
        { status: 400 }
      );
    }

    // Check if appeal deadline has passed
    if (restrictionData.appeal_deadline && new Date(restrictionData.appeal_deadline) < new Date()) {
      return NextResponse.json(
        { error: 'The appeal deadline for this restriction has passed' },
        { status: 400 }
      );
    }

    // Check if user has already submitted an appeal for this restriction
    const [existingAppeal] = await db.execute(`
      SELECT id, status
      FROM restriction_appeals
      WHERE restriction_id = ? AND user_id = ?
      AND status IN ('submitted', 'under_review')
    `, [validatedData.restrictionId, userId]);

    if (Array.isArray(existingAppeal) && existingAppeal.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted an appeal for this restriction' },
        { status: 409 }
      );
    }

    // Calculate response deadline (3-5 business days)
    const responseDeadline = new Date();
    responseDeadline.setDate(responseDeadline.getDate() + 5);

    // Insert the appeal
    const [result] = await db.execute(`
      INSERT INTO restriction_appeals (
        restriction_id, user_id, appeal_reason, supporting_documents,
        status, priority, submitted_at, response_deadline, created_at
      ) VALUES (?, ?, ?, ?, 'submitted', 'medium', NOW(), ?, NOW())
    `, [
      validatedData.restrictionId,
      userId,
      validatedData.appealReason,
      validatedData.supportingDocuments ? JSON.stringify(validatedData.supportingDocuments) : null,
      responseDeadline
    ]);

    const appealId = (result as any).insertId;

    // Send notifications
    try {
      // Notify user of successful appeal submission
      await sendRestrictionNotification({
        userId,
        type: 'appeal_submitted',
        title: 'Appeal Submitted Successfully',
        message: `Your appeal for the ${restrictionData.restriction_type.replace('_', ' ')} restriction has been submitted and will be reviewed within 3-5 business days.`,
        data: {
          restrictionType: restrictionData.restriction_type,
          appealDeadline: responseDeadline.toISOString(),
          canCommunicate: true
        },
        restrictionId: validatedData.restrictionId,
        appealId,
        severity: 'medium'
      });

      // Notify admins with real-time updates
      await sendAdminRestrictionNotification({
        type: 'appeal_submitted',
        title: 'New Restriction Appeal Submitted',
        message: `User has submitted an appeal for ${restrictionData.restriction_type.replace('_', ' ')} restriction`,
        data: {
          restrictionType: restrictionData.restriction_type,
          appealReason: validatedData.appealReason.substring(0, 100) + '...',
          hasDocuments: Boolean(validatedData.supportingDocuments?.length)
        },
        priority: 'medium',
        userId,
        restrictionId: validatedData.restrictionId,
        appealId
      });
    } catch (notificationError) {
      console.error('Failed to send appeal notifications:', notificationError);
      // Don't fail the request if notification fails
    }

    // Log the action
    try {
      await logAdminAction({
        adminId: userId, // User is acting as the initiator
        actionType: 'appeal_reviewed',
        targetType: 'restriction_appeal',
        targetId: appealId,
        affectedUserId: userId,
        actionDetails: {
          restrictionId: validatedData.restrictionId,
          restrictionType: restrictionData.restriction_type,
          appealReason: validatedData.appealReason
        },
        reason: 'User submitted restriction appeal'
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Appeal submitted successfully. We will review it within 3-5 business days.',
      appealId,
      responseDeadline
    }, { status: 201 });

  } catch (error) {
    console.error('Error submitting appeal:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to submit appeal. Please try again.' },
      { status: 500 }
    );
  }
}
