import { NextRequest, NextResponse } from 'next/server';
import { restrictionScheduler } from '@/services/restrictionScheduler';

/**
 * Cron job endpoint for processing expired restrictions
 * This can be called by external cron services like Vercel Cron, GitHub Actions, etc.
 * 
 * Usage:
 * - Set up a cron job to call this endpoint every 5-10 minutes
 * - Include the CRON_SECRET in the Authorization header for security
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get('Authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (!cronSecret) {
      console.error('CRON_SECRET environment variable not set');
      return NextResponse.json(
        { error: 'Cron secret not configured' },
        { status: 500 }
      );
    }

    if (!authHeader || authHeader !== `Bearer ${cronSecret}`) {
      console.error('Invalid or missing cron secret');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Cron job triggered for restriction processing');

    // Process expired restrictions
    await restrictionScheduler.triggerProcessing();

    return NextResponse.json({
      success: true,
      message: 'Restriction processing completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in cron restriction processing:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process restrictions',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// GET endpoint for health check
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'restriction-cron',
    timestamp: new Date().toISOString()
  });
}
