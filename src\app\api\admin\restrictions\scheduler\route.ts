import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { restrictionScheduler } from '@/services/restrictionScheduler';

// GET endpoint to check scheduler status
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const status = restrictionScheduler.getStatus();

    return NextResponse.json({
      scheduler: {
        isRunning: status.isRunning,
        hasInterval: status.intervalId !== null
      },
      message: status.isRunning ? 'Scheduler is running' : 'Scheduler is stopped'
    });

  } catch (error) {
    console.error('Error checking scheduler status:', error);
    return NextResponse.json(
      { error: 'Failed to check scheduler status' },
      { status: 500 }
    );
  }
}

// POST endpoint to manually trigger processing or control scheduler
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'trigger':
        // Manually trigger restriction processing
        await restrictionScheduler.triggerProcessing();
        return NextResponse.json({
          success: true,
          message: 'Restriction processing triggered successfully'
        });

      case 'start':
        // Start the scheduler
        const intervalMinutes = body.intervalMinutes || 5;
        restrictionScheduler.start(intervalMinutes);
        return NextResponse.json({
          success: true,
          message: `Scheduler started with ${intervalMinutes} minute intervals`
        });

      case 'stop':
        // Stop the scheduler
        restrictionScheduler.stop();
        return NextResponse.json({
          success: true,
          message: 'Scheduler stopped successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "trigger", "start", or "stop"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error controlling scheduler:', error);
    return NextResponse.json(
      { error: 'Failed to control scheduler' },
      { status: 500 }
    );
  }
}
